"use client";

import { useState, useEffect, useRef } from "react";
import {
  Bookmark,
  X,
  ArrowUp,
  MessageCircle,
  Clock,
  Loader2,
} from "lucide-react";
import { useLanguage } from "../contexts/LanguageContext";
import { postService, Post } from "../services/postService";
import { formatTimeAgo } from "../utils/timeUtils";

interface BookmarkPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onPostClick: (post: Post) => void;
  onBookmarkCountChange?: (count: number) => void;
  onPostUpdate: (post: Post) => void;
}

export default function BookmarkPanel({
  isOpen,
  onClose,
  onPostClick,
  onBookmarkCountChange,
  onPostUpdate,
}: BookmarkPanelProps) {
  const { t } = useLanguage();
  const panelRef = useRef<HTMLDivElement>(null);
  const [bookmarkedPosts, setBookmarkedPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Close panel when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        panelRef.current &&
        !panelRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  useEffect(() => {
    if (isOpen) {
      loadBookmarkedPosts();
    }
  }, [isOpen]);

  const loadBookmarkedPosts = async () => {
    setIsLoading(true);
    try {
      const response = await postService.getBookmarks();
      if (response.success && response.data) {
        setBookmarkedPosts(response.data);
        // Notify parent of bookmark count change
        onBookmarkCountChange?.(response.data.length);
      }
    } catch (error) {
      console.error("Error loading bookmarked posts:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Change handleBookmark to accept a post argument
  const handleBookmark = async (post: Post) => {
    const optimistic = {
      ...post,
      isBookmarked: !post.isBookmarked,
    };
    onPostUpdate(optimistic);

    if (optimistic.isBookmarked) {
      await postService.saveBookmark(post.id);
    } else {
      await postService.removeBookmark(post.id);
    }

    // Call handlePostUpdate to update the bookmarked posts
    handlePostUpdate(optimistic);
  };

  const formatTimeAgo = (createdAt?: string) => {
    if (!createdAt) return "Recently";

    const now = new Date();
    const postTime = new Date(createdAt);
    const diffInMinutes = Math.floor(
      (now.getTime() - postTime.getTime()) / (1000 * 60)
    );

    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const handlePostClick = (post: Post) => {
    onPostClick(post);
    onClose();
  };

  const handlePostUpdate = (updatedPost: Post) => {
    setBookmarkedPosts((prev) => {
      const updated = prev.map((p) =>
        p.id === updatedPost.id ? updatedPost : p
      );
      const newPosts = updatedPost.isBookmarked
        ? updated.some((p) => p.id === updatedPost.id)
          ? updated
          : [...updated, updatedPost]
        : updated.filter((p) => p.id !== updatedPost.id);

      // Notify parent of bookmark count change
      onBookmarkCountChange?.(newPosts.length);
      return newPosts;
    });
  };

  const handleUnsave = async (postId: string) => {
    try {
      const result = await postService.removeBookmark(postId);
      if (result.success) {
        // Remove from local state
        setBookmarkedPosts((prev) => {
          const updated = prev.filter((p) => p.id !== postId);
          onBookmarkCountChange?.(updated.length);
          return updated;
        });
      }
    } catch (error) {
      console.error("Error removing bookmark:", error);
    }
  };

  if (!isOpen) return null;

  return (
    <div
      ref={panelRef}
      className="z-[999] fixed inset-x-0 bottom-0 top-auto w-full max-w-full min-h-[200px] sm:absolute sm:top-full sm:right-0 sm:left-auto sm:mt-2 sm:w-80 sm:max-w-[20rem] sm:rounded-lg sm:shadow-xl sm:border sm:border-gray-200 max-h-[80vh] sm:max-h-96 rounded-t-xl shadow-2xl border-t border-gray-200 bg-white overflow-y-auto p-1 sm:p-0 animate-in fade-in slide-in-from-bottom duration-200"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 sticky top-0 bg-white z-10 rounded-t-xl">
        <div className="flex items-center space-x-2">
          <Bookmark className="h-5 w-5 text-[#2c6f4a] fill-current" />
          <h2 className="text-lg font-semibold">Bookmarks</h2>
          {bookmarkedPosts.length > 0 && (
            <span className="bg-[#2c6f4a] text-white text-xs px-2 py-1 rounded-full">
              {bookmarkedPosts.length}
            </span>
          )}
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors text-2xl sm:text-base"
          aria-label="Close bookmarks"
        >
          <X className="h-5 w-5" />
        </button>
      </div>

      {/* Content */}
      <div className="max-h-80 overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center space-x-3">
              <div className="w-5 h-5 border-2 border-[#2c6f4a] border-t-transparent rounded-full animate-spin"></div>
              <span className="text-gray-600">Loading bookmarks...</span>
            </div>
          </div>
        ) : bookmarkedPosts.length === 0 ? (
          <div className="text-center py-8">
            <Bookmark className="h-12 w-12 text-gray-300 mx-auto mb-3" />
            <h3 className="font-medium text-gray-900 mb-1">No bookmarks yet</h3>
            <p className="text-sm text-gray-500">
              Save posts to read them later
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {bookmarkedPosts.map((post) => (
              <div
                key={post.id}
                className="p-4 hover:bg-gray-50 transition-colors group"
              >
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-[#ffc35a] to-[#ffb84d] rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-xs font-medium text-white">
                      {post.author
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </span>
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {post.author}
                      </p>
                      <span className="text-xs text-gray-500">
                        {post.authorTitle}
                      </span>
                    </div>

                    <h4
                      onClick={() => handlePostClick(post)}
                      className="text-sm font-medium text-gray-800 mb-1 line-clamp-2 cursor-pointer hover:text-[#2c6f4a]"
                    >
                      {post.question}
                    </h4>

                    <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                      {post.content}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 text-xs text-gray-500">
                        <div className="flex items-center space-x-1">
                          <ArrowUp className="h-3 w-3" />
                          <span>{post.upvotes}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MessageCircle className="h-3 w-3" />
                          <span>{post.commentCount}</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="flex items-center space-x-1 text-xs text-gray-500">
                          <Clock className="h-3 w-3" />
                          <span>{formatTimeAgo(post.createdAt)}</span>
                        </div>
                        {/* Bookmark/Unbookmark Button */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleBookmark(post);
                          }}
                          className="text-[#2c6f4a] hover:text-[#1e4d2b] p-1"
                          title={post.isBookmarked ? "Unbookmark" : "Bookmark"}
                        >
                          <Bookmark className="h-4 w-4" fill={post.isBookmarked ? '#2c6f4a' : 'none'} />
                        </button>
                        {/* Unsave Button */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleUnsave(post.id);
                          }}
                          className="opacity-0 group-hover:opacity-100 transition-opacity text-red-500 hover:text-red-700 p-1"
                          title="Remove bookmark"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      {bookmarkedPosts.length > 0 && (
        <div className="p-3 border-t border-gray-200 bg-gray-50 sticky bottom-0 z-10">
          <p className="text-xs text-gray-500 text-center">
            Click on any post to view details
          </p>
        </div>
      )}
    </div>
  );
}
