"use client";

import { useState, useEffect, useRef } from "react";
import {
  ArrowUp,
  ArrowDown,
  MessageCircle,
  Share,
  Bookmark,
  MoreHorizontal,
  Bot,
  Co<PERSON>,
  Check,
  X,
  Send,
  Edit3,
  Trash2,
  Loader2,
} from "lucide-react";
import { useAuth } from "../contexts/AuthContext";
import { useLanguage } from "../contexts/LanguageContext";
import { Post, Comment, postService } from "../services/postService";
import ComingSoonModal from "./ComingSoonModal";

interface EnhancedPostProps {
  post: Post;
  onPostUpdate: (updatedPost: Post) => void;
  onPostDelete: (postId: string) => void;
}

export default function EnhancedPost({
  post,
  onPostUpdate,
  onPostDelete,
}: EnhancedPostProps) {
  const { user } = useAuth();
  const { translatePost, translateText } = useLanguage();
  const [isExpanded, setIsExpanded] = useState(false);
  const [commentInput, setCommentInput] = useState("");
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [editCommentText, setEditCommentText] = useState("");
  const [editingReply, setEditingReply] = useState<string | null>(null);
  const [editReplyText, setEditReplyText] = useState("");

  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [aiResponseModal, setAiResponseModal] = useState(false);
  const [aiSummaryModal, setAiSummaryModal] = useState(false);
  const [showCommentsModal, setShowCommentsModal] = useState(false);
  const [aiResponse, setAiResponse] = useState("");
  const [aiSummary, setAiSummary] = useState("");
  const [copiedLink, setCopiedLink] = useState(false);
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>(
    {}
  );
  const [shareLinks, setShareLinks] = useState<any>(null);
  const [replyInputs, setReplyInputs] = useState<Record<string, string>>({});
  const [replyLoading, setReplyLoading] = useState<Record<string, boolean>>({});

  const [openDropdown, setOpenDropdown] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingPost, setEditingPost] = useState(false);
  const [editTitle, setEditTitle] = useState("");
  const [editContent, setEditContent] = useState("");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const postRef = useRef<HTMLDivElement>(null);
  const [editedPost, setEditedPost] = useState<Post | null>(null);

  const [showFullContent, setShowFullContent] = useState(false);
  const [comingSoonModalOpen, setComingSoonModalOpen] = useState(false);
  const [posts, setPosts] = useState([]);
  const CONTENT_PREVIEW_LENGTH = 200; // Characters to show before "Read more"

  const shouldShowReadMore = (content: string) => {
    return content.length > CONTENT_PREVIEW_LENGTH;
  };

  const getDisplayContent = (content: string) => {
    if (!shouldShowReadMore(content) || showFullContent) {
      return content;
    }
    return content.substring(0, CONTENT_PREVIEW_LENGTH) + "...";
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setOpenDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Close expanded comments when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isExpanded &&
        postRef.current &&
        !postRef.current.contains(event.target as Node)
      ) {
        setIsExpanded(false);
      }
    };

    if (isExpanded) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isExpanded]);

  const handlePostEdit = async () => {
    if (!editTitle.trim() || !editContent.trim()) {
      alert("Title and content cannot be empty");
      return;
    }

    try {
      console.log("Updating post:", {
        id: post.id,
        title: editTitle,
        content: editContent,
      });

      const response = await postService.updatePost(post.id, {
        title: editTitle,
        content: editContent,
      });

      console.log("Update response:", response);

      if (response.success) {
        const updatedPost = {
          ...post,
          question: editTitle,
          content: editContent,
        };
        onPostUpdate(updatedPost);
        setEditingPost(false);
        setOpenDropdown(false);
      } else {
        console.error("Failed to update post:", response.error);
        alert("Failed to update post: " + (response.error || "Unknown error"));
      }
    } catch (error) {
      console.error("Error updating post:", error);
      alert("Network error. Please try again.");
    }
  };

  // Handle post delete

  const [deleted, setDeleted] = useState(false);
  const handlePostDelete = () => {
    if (post.id) {
      onPostDelete(post.id);
      setShowDeleteModal(false);
      setOpenDropdown(false);
    }
  };

  // Start editing
  const startEdit = () => {
    setEditingPost(true);
    setEditTitle(post.question);
    setEditContent(post.content);
    setOpenDropdown(false);
  };

  // Cancel editing
  const cancelEdit = () => {
    setEditingPost(false);
    setEditTitle("");
    setEditContent("");
  };

  // Translate the post content
  const [translatedPost, setTranslatedPost] = useState(translatePost(post));
  // Calculate total comments including all nested replies
  const getTotalCommentCount = () => {
    const countReplies = (comments: Comment[]): number => {
      return comments.reduce((total: number, comment: Comment) => {
        return (
          total + 1 + (comment.replies ? countReplies(comment.replies) : 0)
        );
      }, 0);
    };
    return countReplies(post.comments || []);
  };

  const totalCommentCount = getTotalCommentCount();

  // Format time ago
  const formatTimeAgo = (createdAt?: string) => {
    if (!createdAt) return post.timeAgo;

    const now = new Date();
    const postTime = new Date(createdAt);
    const diffInHours = Math.floor(
      (now.getTime() - postTime.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24)
      return `${diffInHours} hour${diffInHours > 1 ? "s" : ""} ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} day${diffInDays > 1 ? "s" : ""} ago`;
  };

  // Handle voting
  const handleVote = async (voteType: "upvote" | "downvote") => {
    const currentlyUpvoted = post.hasUpvoted;
    const currentlyDownvoted = post.hasDownvoted;

    let newUpvotes = post.upvotes;
    let newDownvotes = post.downvotes;
    let newHasUpvoted = false;
    let newHasDownvoted = false;

    if (voteType === "upvote") {
      if (currentlyUpvoted) {
        // Remove upvote
        newUpvotes--;
      } else {
        // Add upvote
        newUpvotes++;
        newHasUpvoted = true;
        // Remove downvote if exists
        if (currentlyDownvoted) {
          newDownvotes--;
        }
      }
    } else {
      if (currentlyDownvoted) {
        // Remove downvote
        newDownvotes--;
      } else {
        // Add downvote
        newDownvotes++;
        newHasDownvoted = true;
        // Remove upvote if exists
        if (currentlyUpvoted) {
          newUpvotes--;
        }
      }
    }

    const updatedPost = {
      ...post,
      upvotes: newUpvotes,
      downvotes: newDownvotes,
      hasUpvoted: newHasUpvoted,
      hasDownvoted: newHasDownvoted,
    };

    onPostUpdate(updatedPost);

    // Call API to update vote
    await postService.updatePostVote(
      post.id,
      voteType
    );
  };

  // Handle bookmark toggle
  const handleBookmark = async () => {
    // Check if user is logged in
    const currentUser = JSON.parse(localStorage.getItem("user") || "{}");
    const token = localStorage.getItem("token");

    if (!currentUser.id || !token) {
      console.error("User not authenticated");
      alert("Please log in to bookmark posts");
      return;
    }

    console.log("Bookmark clicked, current state:", post.isBookmarked);

    const newBookmarkState = !post.isBookmarked;

    // Optimistic update - immediately update UI
    const optimisticPost = {
      ...post,
      isBookmarked: newBookmarkState,
    };
    onPostUpdate(optimisticPost);

    try {
      let result;
      if (newBookmarkState) {
        // Save bookmark
        console.log("Saving bookmark for post:", post.id);
        result = await postService.saveBookmark(post.id);
      } else {
        // Remove bookmark
        console.log("Removing bookmark for post:", post.id);
        result = await postService.removeBookmark(post.id);
      }

      console.log("Bookmark operation result:", result);

      if (!result.success) {
        // Revert on failure
        onPostUpdate(post);
        console.error("Bookmark operation failed:", result.error);
        alert(`Bookmark operation failed: ${result.error}`);
      } else {
        console.log(
          "Bookmark operation successful, new state:",
          newBookmarkState
        );
      }
    } catch (error) {
      console.error("Bookmark operation failed:", error);
      // Revert on error
      onPostUpdate(post);
      alert("Bookmark operation failed");
    }
  };

  // Handle comment submission
  const handleCommentSubmit = async () => {
    if (!commentInput.trim() || !user) return;

    setLoadingStates((prev) => ({ ...prev, [`comment-${post.id}`]: true }));

    try {
      const response = await postService.addComment(
        post.id,
        commentInput.trim()
      );

      console.log("🟢 Comment API result:", response);

      if (response.success && response.data) {
        const newComment = {
          ...response.data,
          id: response.data.id || `temp-${Date.now()}`,
          author: user.name || "Anonymous",
          authorTitle: user.interest || "Community Member",
          authorCity: user.city || "",
          authorCountry: "",
          timeAgo: "Just now",
          isUserComment: true,
          replies: [],
          content: response.data.content || commentInput.trim(), // ✅ Ensure content is set
        };

        console.log("🟢 New comment object:", newComment);

        const updatedPost = {
          ...post,
          comments: [...post.comments, newComment],
          commentCount: (post.commentCount ?? post.comments.length) + 1,
        };

        onPostUpdate(updatedPost);
        setCommentInput("");
      }
    } catch (error) {
      console.error("Error adding comment:", error);
    } finally {
      setLoadingStates((prev) => ({ ...prev, [`comment-${post.id}`]: false }));
    }
  };

  // Handle comment delete
  const handleCommentDelete = async (commentId: string) => {
    try {
      await postService.deleteComment(commentId);

      // Remove the comment and all its replies from the UI
      const updatedPost = {
        ...post,
        comments: post.comments.filter((c) => c.id !== commentId),
        commentCount: (post.commentCount ?? post.comments.length) - 1,
      };
      onPostUpdate(updatedPost);
    } catch (error) {
      console.error("Error deleting comment:", error);
    }
  };
  // Handle reply delete (move this here)
  const handleReplyDelete = async (replyId: string) => {
    try {
      await postService.deleteReply(replyId);
      const updatedComments = post.comments.map((comment) => ({
        ...comment,
        replies: comment.replies.filter((reply) => reply.id !== replyId),
      }));
      onPostUpdate({ ...post, comments: updatedComments });
    } catch (err) {
      console.error("Delete reply failed:", err);
    }
  };

  // Handle comment edit
  const handleNestedReply = async (commentId: string, questionId: string) => {
    const replyContent = replyInputs[commentId]?.trim();
    if (!replyContent || !user) return;

    setReplyLoading((prev) => ({ ...prev, [commentId]: true }));

    try {
      const response = await postService.addReplyToComment(
        commentId, // ✅ parent reply ID
        replyContent, // ✅ reply content
        user.id || "demo-user", // ✅ current user ID
        questionId // ✅ new: pass questionId (post.id)
      );

      console.log("🟢 Nested Reply API result:", response);

      if (response.success && response.data) {
        const newReply = {
          ...response.data,
          id: response.data.id || `temp-reply-${Date.now()}`,
          author: user.name || "Anonymous",
          authorTitle: user.interest || "Community Member",
          authorCity: user.city || "",
          authorCountry: "",
          timeAgo: "Just now",
          isUserComment: true,
          content: response.data.content || replyContent, // ✅ Ensure content is set
        };

        console.log("🟢 New reply object:", newReply);

        // Update the specific comment with the new reply
        const updatedComments = post.comments.map((c) => {
          if (c.id === commentId) {
            return {
              ...c,
              replies: [...(c.replies || []), newReply],
            };
          }
          return c;
        });

        const updatedPost = {
          ...post,
          comments: updatedComments,
        };

        onPostUpdate(updatedPost);
        setReplyInputs((prev) => ({ ...prev, [commentId]: "" }));
      }
    } catch (error) {
      console.error("Error posting reply:", error);
    } finally {
      setReplyLoading((prev) => ({ ...prev, [commentId]: false }));
    }
  };

  // Handle share
  const handleShare = async () => {
    const url = `${window.location.origin}/?postId=${post.id}`;
    try {
      await navigator.clipboard.writeText(url);
      setCopiedLink(true);
      setTimeout(() => setCopiedLink(false), 2000);
    } catch (error) {
      console.error("Failed to copy link:", error);
    }
  };

  // Handle platform-specific sharing
  const handlePlatformShare = async (platform: string) => {
    try {
      const response = await postService.shareToSpecificPlatform(
        post.id,
        platform
      );
      if (response.success && response.link) {
        window.open(response.link, "_blank");
      }
    } catch (error) {
      console.error("Failed to share to platform:", error);
    }
  };

  // Fetch share links when modal opens
  const fetchShareLinks = async () => {
    try {
      const response = await postService.getShareLinks(post.id);
      if (response.success && response.links) {
        setShareLinks(response.links);
      }
    } catch (error) {
      console.error("Failed to fetch share links:", error);
    }
  };

  // Update share modal open handler
  const handleShareModalOpen = () => {
    setShareModalOpen(true);
    fetchShareLinks();
  };

  // Handle AI response
  const handleAIResponse = () => {
    setComingSoonModalOpen(true);
  };

  // Handle AI summary
  const handleAISummary = () => {
    setComingSoonModalOpen(true);
  };

  const handleCommentEdit = async (commentId: string) => {
    if (!editCommentText.trim()) return;

    try {
      const res = await postService.editComment(commentId, {
        content: editCommentText,
      });

      if (res.success) {
        const updatedPost = { ...post };
        const commentIndex = updatedPost.comments.findIndex(
          (c) => c.id === commentId
        );

        if (commentIndex !== -1) {
          updatedPost.comments[commentIndex].content = editCommentText;
          onPostUpdate(updatedPost); // ✅ FIXED
        }

        setEditingComment(null);
        setEditCommentText("");
      }
    } catch (err) {
      console.error("Failed to edit comment:", err);
    }
  };

  // Handle reply edit
  const handleReplyEdit = async (replyId: string) => {
    if (!editReplyText.trim()) return;

    try {
      const res = await postService.editComment(replyId, {
        content: editReplyText,
      });

      if (res.success) {
        const updatedComments = post.comments.map((comment) => ({
          ...comment,
          replies: comment.replies.map((reply) =>
            reply.id === replyId ? { ...reply, content: editReplyText } : reply
          ),
        }));

        onPostUpdate({ ...post, comments: updatedComments });
        setEditingReply(null);
        setEditReplyText("");
      }
    } catch (err) {
      console.error("Failed to edit reply:", err);
    }
  };

  const [showReplyInput, setShowReplyInput] = useState<{
    [key: string]: boolean;
  }>({});

  // Toggle reply input visibility
  const toggleReplyInput = (commentId: string) => {
    setShowReplyInput((prev) => ({
      ...prev,
      [commentId]: !prev[commentId],
    }));
  };

  useEffect(() => {
    // Check if this post is bookmarked when component mounts
    const checkBookmarkStatus = async () => {
      try {
        const isBookmarked = await postService.isPostBookmarked(post.id);
        if (isBookmarked !== post.isBookmarked) {
          onPostUpdate({
            ...post,
            isBookmarked,
          });
        }
      } catch (error) {
        console.error("Error checking bookmark status:", error);
      }
    };

    // Only check on initial mount, not on every post update
    checkBookmarkStatus();
  }, [post.id]); // Remove onPostUpdate from dependencies

  // Add this debug log near the top of the component
  console.log("Post comment count:", post.commentCount);
  console.log("Post comments length:", post.comments?.length);
  console.log("Show comments modal:", showCommentsModal);
  console.log("Is expanded:", isExpanded);

  // Close comments modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement | null;
      if (showCommentsModal && target && !target.closest(".comments-modal")) {
        setShowCommentsModal(false);
      }
    };

    if (showCommentsModal) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showCommentsModal]);

  const handlePostUpdate = () => {
    console.log("Save clicked", editedPost);

    if (onPostUpdate && editedPost) {
      onPostUpdate(editedPost);
      setEditingPost(false);
      alert("Post updated successfully!");
    } else {
      alert("Could not update post");
    }
  };

  const [showDeleteCommentModal, setShowDeleteCommentModal] = useState<string | null>(null);
  const [showDeleteReplyModal, setShowDeleteReplyModal] = useState<string | null>(null);

  // Update delete handlers to show modal
  const handleDeleteCommentClick = (commentId: string) => {
    setShowDeleteCommentModal(commentId);
  };
  const handleDeleteReplyClick = (replyId: string) => {
    setShowDeleteReplyModal(replyId);
  };

  return (
    <>
      <div
        ref={postRef}
        className="bg-gradient-to-br from-white to-green-50/30 rounded-lg shadow-lg border border-green-200/50 backdrop-blur-sm overflow-hidden"
      >
        {/* Post Header */}
        <div className="p-4 border-b border-green-200/30 sm:p-2">
          <div className="flex items-start justify-between gap-2">
            <div className="flex items-center space-x-3 sm:space-x-2">
              {/* In the post header, update the user icon circle: */}
              <div className="w-10 h-10 sm:w-8 sm:h-8 bg-gradient-to-r from-[#ffc35a] to-[#ffb84d] rounded-full flex items-center justify-center shadow-lg shrink-0">
                <span className="text-sm sm:text-xs font-medium text-white">
                  {translatedPost.author.split(" ").map((n: string) => n[0]).join("")}
                </span>
              </div>
              {/* In the post header, use responsive layout: */}
              <div className="flex flex-col">
                {/* Username row */}
                <div className="flex items-center space-x-2">
                  <h4 className="font-medium bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] bg-clip-text text-transparent text-base sm:text-lg break-words">
                    {translatedPost.author}
                  </h4>
                  {/* Show role next to username on mobile only */}
                  <span className="text-xs text-gray-500 sm:hidden">{translatedPost.authorTitle}</span>
                </div>
                {/* Info row: city, time, role, badge */}
                <div className="flex items-center flex-wrap gap-x-2 text-xs text-gray-500 sm:mt-1">
                  {translatedPost.authorCity && <span>{translatedPost.authorCity}</span>}
                  {translatedPost.authorCountry && <span>• {translatedPost.authorCountry}</span>}
                  <span>• {translatedPost.timeAgo}</span>
                  <span className="hidden sm:inline">•</span>
                  <span className="text-xs text-gray-500 sm:inline hidden">{translatedPost.authorTitle}</span>
                  {post.isUserPost && (
                    <span className="ml-2 px-1 py-0.5 text-xs sm:px-2 sm:py-1 sm:text-xs font-normal sm:font-medium text-gray-500 sm:text-white bg-transparent sm:bg-gradient-to-r sm:from-blue-500 sm:to-blue-600 sm:rounded-full border-none">
                      {translateText("Your Post")}
                    </span>
                  )}
                </div>
              </div>
            </div>
            {/* In the post header, after the author info and before the 3-dots menu, add back the Share and Save buttons, styled for mobile: */}
            <div className="flex items-center space-x-2 sm:space-x-1">
              {/* Share */}
              <button
                onClick={handleShareModalOpen}
                className="flex items-center space-x-1 text-gray-400 hover:text-[#2c6f4a] hover:bg-green-50 px-2 py-1 rounded-full transition-all duration-200 sm:px-1 sm:py-0"
                aria-label="Share post"
              >
                <Share className="h-4 w-4 sm:h-5 sm:w-5" />
                <span className="hidden sm:inline text-xs">{translateText("Share")}</span>
              </button>
              {/* Bookmark */}
              <button
                onClick={handleBookmark}
                className={`flex items-center space-x-1 px-2 py-1 rounded-full transition-all duration-200 text-xs sm:px-1 sm:py-0 sm:text-sm ${
                  post.isBookmarked
                    ? "text-yellow-600 bg-yellow-50 border border-yellow-200"
                    : "text-gray-400 hover:text-yellow-600 hover:bg-yellow-50"
                }`}
                aria-label={post.isBookmarked ? "Saved" : "Save"}
              >
                <Bookmark
                  className={`h-4 w-4 sm:h-5 sm:w-5 transition-all duration-200 ${
                    post.isBookmarked ? "fill-yellow-600 text-yellow-600" : ""
                  }`}
                />
                <span className="hidden sm:inline">
                  {post.isBookmarked ? translateText("Saved") : translateText("Save")}
                </span>
              </button>
              {/* Three Dots Menu - Always visible for user's own posts, mobile-friendly */}
              {post.isUserPost && (
                <div className="relative" ref={dropdownRef}>
                  <button
                    onClick={() => setOpenDropdown(!openDropdown)}
                    className="text-gray-400 hover:text-gray-600 hover:bg-gray-50 p-1 rounded-full transition-colors duration-200 sm:p-1"
                    aria-label="Post options"
                  >
                    <MoreHorizontal className="h-5 w-5 sm:h-6 sm:w-6" />
                  </button>
                  {openDropdown && (
                    <div className="absolute right-0 mt-2 w-32 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                      <button
                        onClick={startEdit}
                        className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
                      >
                        <Edit3 className="h-3 w-3" />
                        <span>Edit</span>
                      </button>
                      <button
                        onClick={() => {
                          setShowDeleteModal(true);
                          setOpenDropdown(false);
                        }}
                        className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
                      >
                        <Trash2 className="h-3 w-3" />
                        <span>Delete</span>
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Post Content */}
        <div className="p-4">
          <h3 className="font-semibold text-lg sm:text-base text-gray-900 mb-2 sm:mb-1 break-words max-w-full">
            {translatedPost.question}
          </h3>
          <div className="text-gray-700 leading-relaxed sm:text-sm sm:leading-snug break-words max-w-full">
            <p className="whitespace-pre-wrap break-words max-w-full">
              {getDisplayContent(translatedPost.content)}
            </p>

            {/* Read More/Less Button */}
            {shouldShowReadMore(translatedPost.content) && (
              <button
                onClick={() => setShowFullContent(!showFullContent)}
                className="mt-2 text-[#2c6f4a] hover:text-[#3a8b5c] font-medium text-sm sm:text-xs transition-colors duration-200 flex items-center space-x-1"
              >
                <span>{showFullContent ? "Read less" : "Read more"}</span>
                <svg
                  className={`w-4 h-4 transition-transform duration-200 ${
                    showFullContent ? "rotate-180" : ""
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
            )}
          </div>
        </div>

        {/* Post Actions */}
        <div className="px-4 py-3 border-t border-green-200/30">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* Upvote */}
              <button
                onClick={() => handleVote("upvote")}
                className={`flex items-center space-x-2 px-2 py-1 rounded-full transition-all duration-200 ${
                  post.hasUpvoted
                    ? "text-green-600 bg-green-50"
                    : "text-gray-400 hover:text-green-600 hover:bg-green-50"
                }`}
              >
                <ArrowUp className="h-5 w-5" />
                <span className="text-sm">{post.upvotes}</span>
              </button>

              {/* Downvote */}
              <button
                onClick={() => handleVote("downvote")}
                className={`flex items-center space-x-2 px-2 py-1 rounded-full transition-all duration-200 ${
                  post.hasDownvoted
                    ? "text-red-600 bg-red-50"
                    : "text-gray-400 hover:text-red-600 hover:bg-red-50"
                }`}
              >
                <ArrowDown className="h-5 w-5" />
                <span className="text-sm">{post.downvotes}</span>
              </button>

              {/* Comments */}
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="flex items-center space-x-2 text-gray-400 hover:text-[#2c6f4a] hover:bg-green-50 px-2 py-1 rounded-full transition-all duration-200"
              >
                <MessageCircle className="h-5 w-5" />
                <span className="text-sm">{totalCommentCount}</span>
              </button>
            </div>

            <div className="flex items-center space-x-4">
              {/* AI Response - Pink */}
              <button
                onClick={handleAIResponse}
                className="flex items-center space-x-1 text-pink-500 hover:underline text-xs sm:text-sm"
              >
                <Bot className="w-4 h-4" />
                <span className="sm:hidden">AI Response</span><span className="hidden sm:inline">Check for AI Response</span>
              </button>

              {/* AI Summary - Blue */}
              {(post.comments?.length > 0 || (post.commentCount ?? post.comments?.length) > 0) && (
                <button
                  onClick={handleAISummary}
                  className="flex items-center space-x-1 text-blue-500 hover:underline text-xs sm:text-sm"
                >
                  <Bot className="w-4 h-4" />
                  <span className="sm:hidden">Comments AI Summary</span><span className="hidden sm:inline">All Comments AI Summary</span>
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Expanded Comments Section */}
        {isExpanded && (
          <div className="border-t border-green-200/30 bg-gray-50/50">
            {/* Add Comment */}
            {user && (
              <div className="p-4 border-b border-green-200/30">
                <div className="flex space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-white">
                      {user.name
                        ?.split(" ")
                        .map((n: string) => n[0])
                        .join("") || "U"}
                    </span>
                  </div>
                  <div className="flex-1">
                    <textarea
                      value={commentInput}
                      onChange={(e) => setCommentInput(e.target.value)}
                      placeholder="Write a comment..."
                      rows={2}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2c6f4a]/20 focus:border-[#2c6f4a] resize-none text-sm text-black"
                    />
                    <div className="flex justify-between items-center mt-2">
                      {/* AI Summary Button - Only show when there are comments */}
                      <div>
                        {(post.commentCount ?? post.comments?.length) > 0 && (
                          <button
                            onClick={handleAISummary}
                            disabled={loadingStates[`summary-${post.id}`]}
                            className="flex items-center space-x-1 text-[11px] sm:text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50 px-2 py-1 rounded-full transition-all duration-200"
                          >
                            {loadingStates[`summary-${post.id}`] ? (
                              <Loader2 className="h-3 w-3 animate-spin" />
                            ) : (
                              <Bot className="h-3 w-3" />
                            )}
                            <span className="sm:hidden">Comments AI Summary</span><span className="hidden sm:inline">{translateText("All Comments AI Summary")}</span>
                          </button>
                        )}
                      </div>

                      {/* Submit Comment Button */}
                      <button
                        onClick={handleCommentSubmit}
                        disabled={
                          !commentInput.trim() ||
                          loadingStates[`comment-${post.id}`]
                        }
                        className="flex items-center space-x-1 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] text-white px-3 py-1 rounded-md hover:from-[#3a8b5c] hover:to-[#2c6f4a] transition-all duration-200 disabled:opacity-50 text-sm"
                      >
                        {loadingStates[`comment-${post.id}`] ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          <Send className="h-3 w-3" />
                        )}
                        <span>Comment</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Comments List */}
            <div className="max-h-96 overflow-y-auto">
              {post.comments.map((comment, index) => (
                <div
                  key={comment.id || `comment-${index}-${post.id}`} // Fallback key
                  className="p-4 border-b border-green-200/30 last:border-b-0"
                >
                  <div className="flex space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-[#ffc35a] to-[#ffb84d] rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-white">
                        {comment.author
                          .split(" ")
                          .map((n: string) => n[0])
                          .join("")}
                      </span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h5 className="font-medium text-sm text-gray-900">
                          {comment.author}
                        </h5>
                        <span className="text-xs text-gray-500">
                          {comment.authorTitle}
                        </span>
                        <span className="text-xs text-gray-400">
                          • {comment.timeAgo}
                        </span>
                      </div>

                      {editingComment === comment.id ? (
                        <div className="space-y-2">
                          <textarea
                            value={editCommentText}
                            onChange={(e) => setEditCommentText(e.target.value)}
                            rows={2}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2c6f4a]/20 focus:border-[#2c6f4a] resize-none text-sm"
                          />
                          <div className="flex space-x-2">
                            <button
                              key={`save-comment-${comment.id}`}
                              onClick={() => handleCommentEdit(comment.id)}
                              className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                            >
                              Save
                            </button>
                            <button
                              key={`cancel-comment-${comment.id}`}
                              onClick={() => {
                                setEditingComment(null);
                                setEditCommentText("");
                              }}
                              className="text-xs bg-gray-500 text-white px-2 py-1 rounded hover:bg-gray-600"
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      ) : (
                        <>
                          <p className="text-sm text-gray-700">
                            {comment.content}
                          </p>

                          {/* Comment Edit/Delete Buttons - RIGHT AFTER COMMENT CONTENT */}
                          {comment.isUserComment && (
                            <div className="flex space-x-2 mt-2">
                              <button
                                key={`edit-comment-${comment.id}`}
                                onClick={() => {
                                  setEditingComment(comment.id);
                                  setEditCommentText(comment.content);
                                }}
                                className="text-xs text-blue-600 hover:text-blue-700"
                              >
                                <Edit3 className="h-3 w-3 inline mr-1" />
                                Edit
                              </button>
                              <button
                                key={`delete-comment-${comment.id}`}
                                onClick={() => handleDeleteCommentClick(comment.id)}
                                className="text-xs text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-3 w-3 inline mr-1" />
                                Delete
                              </button>
                            </div>
                          )}

                          {/* Reply Button */}
                          <div className="mt-2">
                            <button
                              onClick={() => toggleReplyInput(comment.id)}
                              className="flex items-center space-x-1 text-xs text-gray-500 hover:text-[#2c6f4a] transition-colors"
                            >
                              <MessageCircle className="h-3 w-3" />
                              <span>
                                {showReplyInput[comment.id]
                                  ? "Cancel Reply"
                                  : "Reply"}
                              </span>
                            </button>
                          </div>
                        </>
                      )}

                      {/* Reply Input Section - Only show when toggled */}
                      {showReplyInput[comment.id] && (
                        <div className="mt-3 space-y-2">
                          <textarea
                            value={replyInputs[comment.id] || ""}
                            onChange={(e) =>
                              setReplyInputs((prev) => ({
                                ...prev,
                                [comment.id]: e.target.value,
                              }))
                            }
                            placeholder="Reply to this comment..."
                            rows={2}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md resize-none text-sm focus:outline-none focus:ring-2 focus:ring-[#2c6f4a]/20 focus:border-[#2c6f4a] text-black placeholder-gray-500"
                          />
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={() => toggleReplyInput(comment.id)}
                              className="text-xs bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600"
                            >
                              Cancel
                            </button>
                            <button
                              onClick={() => {
                                handleNestedReply(comment.id, post.id);
                                setShowReplyInput((prev) => ({
                                  ...prev,
                                  [comment.id]: false,
                                }));
                              }}
                              disabled={replyLoading[comment.id]}
                              className="text-xs bg-[#2c6f4a] text-white px-3 py-1 rounded hover:bg-[#3a8b5c] disabled:opacity-50"
                            >
                              {replyLoading[comment.id]
                                ? "Replying..."
                                : "Reply"}
                            </button>
                          </div>
                        </div>
                      )}

                      {/* Nested Replies Section */}
                      {comment.replies && comment.replies.length > 0 && (
                        <div className="mt-4 ml-6 space-y-3">
                          {comment.replies.map((reply) => (
                            <div
                              key={reply.id}
                              className="p-3 bg-white border border-green-100 rounded-lg shadow-sm relative"
                            >
                              <div className="flex items-center space-x-2 mb-1">
                                <div className="w-6 h-6 bg-gradient-to-r from-[#ffc35a] to-[#ffb84d] rounded-full flex items-center justify-center">
                                  <span className="text-[10px] font-medium text-white">
                                    {reply.author.split(" ").map((n: string, index) => (
                                      <span key={index}>{n[0]}</span>
                                    ))}
                                  </span>
                                </div>
                                <h6 className="text-xs font-medium text-gray-800">
                                  {reply.author}
                                </h6>
                                <span className="text-xs text-gray-500">
                                  • {reply.authorTitle}
                                </span>
                                {reply.authorCity && (
                                  <span
                                    key={`city-${reply.id}`}
                                    className="text-xs text-gray-400"
                                  >
                                    • {reply.authorCity}
                                  </span>
                                )}
                              </div>
                              {editingReply === reply.id ? (
                                <div className="space-y-2 mt-1">
                                  <textarea
                                    value={editReplyText}
                                    onChange={(e) =>
                                      setEditReplyText(e.target.value)
                                    }
                                    rows={2}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md resize-none text-sm"
                                  />
                                  <div className="flex space-x-2">
                                    <button
                                      key={`save-${reply.id}`}
                                      onClick={() => handleReplyEdit(reply.id)}
                                      className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                                    >
                                      Save
                                    </button>
                                    <button
                                      key={`cancel-${reply.id}`}
                                      onClick={() => {
                                        setEditingReply(null);
                                        setEditReplyText("");
                                      }}
                                      className="text-xs bg-gray-500 text-white px-2 py-1 rounded hover:bg-gray-600"
                                    >
                                      Cancel
                                    </button>
                                  </div>
                                </div>
                              ) : (
                                <>
                                  <p className="text-sm text-gray-700">
                                    {reply.content}
                                  </p>

                                  {/* Reply Edit/Delete Buttons */}
                                  {reply.isUserComment && (
                                    <div className="flex space-x-2 mt-2">
                                      <button
                                        key={`edit-reply-${reply.id}`}
                                        onClick={() => {
                                          setEditingReply(reply.id);
                                          setEditReplyText(reply.content);
                                        }}
                                        className="text-xs text-blue-600 hover:text-blue-700"
                                      >
                                        <Edit3 className="h-3 w-3 inline mr-1" />
                                        Edit
                                      </button>
                                      <button
                                        key={`delete-reply-${reply.id}`}
                                        onClick={() => handleDeleteReplyClick(reply.id)}
                                        className="text-xs text-red-600 hover:text-red-700"
                                      >
                                        <Trash2 className="h-3 w-3 inline mr-1" />
                                        Delete
                                      </button>
                                    </div>
                                  )}
                                </>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              {post.comments.length === 0 && (
                <div className="p-4 text-center text-gray-500 text-sm">
                  No comments yet. Be the first to comment!
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Share Modal */}
      {shareModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={() => setShareModalOpen(false)}
        >
          <div
            className="bg-gradient-to-br from-white to-green-50/30 rounded-xl shadow-2xl max-w-md w-full relative transform transition-all duration-300 scale-100 border border-green-200/50"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-green-200/30">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] rounded-full flex items-center justify-center shadow-lg">
                  <Share className="h-5 w-5 text-white" />
                </div>
                <h3 className="text-xl font-bold bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] bg-clip-text text-transparent">
                  Share Post
                </h3>
              </div>
              <button
                onClick={() => setShareModalOpen(false)}
                className="text-gray-400 hover:text-[#2c6f4a] p-2 rounded-full hover:bg-green-50 transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="p-6">
              {/* Only Copy Link Section, remove all platform share buttons */}
              <div className="space-y-3">
                <div className="flex items-center space-x-3 p-3 bg-green-50/50 rounded-xl border border-green-200/50">
                  <input
                    type="text"
                    value={
                      shareLinks?.basic ||
                      `${window.location.origin}/?postId=${post.id}`
                    }
                    readOnly
                    className="flex-1 bg-transparent text-sm text-gray-600 focus:outline-none"
                  />
                  <button
                    onClick={handleShare}
                    className="flex items-center space-x-2 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] text-white px-4 py-2 rounded-lg hover:from-[#3a8b5c] hover:to-[#2c6f4a] transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  >
                    {copiedLink ? (
                      <>
                        <Check className="h-4 w-4" />
                        <span className="text-sm font-medium">Copied!</span>
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4" />
                        <span className="text-sm font-medium">Copy</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* AI Response Modal */}
      {aiResponseModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-base sm:text-lg font-semibold flex items-center space-x-2">
                <Bot className="h-5 w-5 text-purple-600" />
                <span>AI Response</span>
              </h3>
              <button
                onClick={() => setAiResponseModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="p-2 sm:p-4">
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <pre className="whitespace-pre-wrap text-xs sm:text-sm text-gray-700">
                  {aiResponse}
                </pre>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* AI Summary Modal */}
      {aiSummaryModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-base sm:text-lg font-semibold flex items-center space-x-2">
                <Bot className="h-5 w-5 text-blue-600" />
                <span>AI Comments Summary</span>
              </h3>
              <button
                onClick={() => setAiSummaryModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="p-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <pre className="whitespace-pre-wrap text-xs sm:text-sm text-gray-700">
                  {aiSummary}
                </pre>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Post Modal */}
      {editingPost && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Edit Post</h3>
              <button
                onClick={cancelEdit}
                className="text-gray-400 hover:text-[#2c6f4a] p-2 rounded-full hover:bg-green-50 transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Title
                </label>
                <input
                  type="text"
                  value={editTitle}
                  onChange={(e) => setEditTitle(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2c6f4a]/20 focus:border-[#2c6f4a] text-black transition-all duration-200"
                  placeholder="Post title"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Content
                </label>
                <textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  rows={6}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2c6f4a]/20 focus:border-[#2c6f4a] text-black resize-none transition-all duration-200"
                  placeholder="Write your content here..."
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  {editContent.length} characters
                </p>
              </div>
              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  onClick={cancelEdit}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors duration-200"
                >
                  Cancel
                </button>
                <button
                  onClick={handlePostEdit}
                  className="px-6 py-2 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] text-white rounded-lg hover:from-[#3a8b5c] hover:to-[#2c6f4a] transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gradient-to-br from-white to-red-50/30 rounded-xl shadow-2xl max-w-sm w-full border border-red-200/50">
            <div className="p-6">
              <div className="flex items-center justify-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-lg">
                  <Trash2 className="h-6 w-6 text-white" />
                </div>
              </div>
              <h3 className="text-xl font-bold text-center mb-2 bg-gradient-to-r from-red-600 to-red-700 bg-clip-text text-transparent">
                Delete Post
              </h3>
              <p className="text-gray-600 mb-6 text-center leading-relaxed">
                Are you sure you want to delete this post? This action cannot be
                undone and all comments will be lost.
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={handlePostDelete}
                  className="flex-1 bg-gradient-to-r from-red-600 to-red-700 text-white py-3 px-4 rounded-lg hover:from-red-700 hover:to-red-800 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  Yes, Delete
                </button>
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="flex-1 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] text-white py-3 px-4 rounded-lg hover:from-[#3a8b5c] hover:to-[#2c6f4a] transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Comments Section - Inline below post */}
      {showCommentsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="comments-modal bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[80vh] overflow-hidden">
            {/* Add Comment Section */}
            {user && (
              <div className="p-4 border-b border-gray-200 bg-white">
                <div className="flex space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-white">
                      {user.name
                        ?.split(" ")
                        .map((n: string) => n[0])
                        .join("") || "U"}
                    </span>
                  </div>
                  <div className="flex-1">
                    <textarea
                      value={commentInput}
                      onChange={(e) => setCommentInput(e.target.value)}
                      placeholder="Write a comment..."
                      rows={2}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2c6f4a]/20 focus:border-[#2c6f4a] resize-none text-sm text-black"
                    />
                    <div className="flex justify-end mt-2">
                      <button
                        onClick={handleCommentSubmit}
                        disabled={
                          !commentInput.trim() ||
                          loadingStates[`comment-${post.id}`]
                        }
                        className="flex items-center space-x-1 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] text-white px-3 py-1 rounded-md hover:from-[#3a8b5c] hover:to-[#2c6f4a] transition-all duration-200 disabled:opacity-50 text-sm"
                      >
                        {loadingStates[`comment-${post.id}`] ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          <Send className="h-3 w-3" />
                        )}
                        <span>Comment</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Comments List */}
            <div className="p-4 max-h-96 overflow-y-auto">
              {post.comments.map((comment, commentIndex) => (
                <div
                  key={comment.id || `comment-${commentIndex}-${post.id}`}
                  className="p-4 border-b border-green-200/30 last:border-b-0"
                >
                  <div className="flex space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-[#ffc35a] to-[#ffb84d] rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-white">
                        {comment.author
                          .split(" ")
                          .map((n: string) => n[0])
                          .join("")}
                      </span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h5 className="font-medium text-sm text-gray-900">
                          {comment.author}
                        </h5>
                        <span className="text-xs text-gray-500">
                          {comment.authorTitle}
                        </span>
                        <span className="text-xs text-gray-400">
                          • {comment.timeAgo}
                        </span>
                      </div>

                      {editingComment === comment.id ? (
                        <div className="space-y-2">
                          <textarea
                            value={editCommentText}
                            onChange={(e) => setEditCommentText(e.target.value)}
                            rows={3}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md resize-none text-sm focus:outline-none focus:ring-2 focus:ring-[#2c6f4a]/20 focus:border-[#2c6f4a]"
                          />
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleCommentEdit(comment.id)}
                              className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                            >
                              Save
                            </button>
                            <button
                              onClick={() => {
                                setEditingComment(null);
                                setEditCommentText("");
                              }}
                              className="text-xs bg-gray-500 text-white px-2 py-1 rounded hover:bg-gray-600"
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      ) : (
                        <>
                          <p className="text-sm text-gray-700 mb-2">
                            {comment.content}
                          </p>

                          {/* Comment Edit/Delete Buttons */}
                          {comment.isUserComment && (
                            <div className="flex space-x-2 mt-2">
                              <button
                                onClick={() => {
                                  setEditingComment(comment.id);
                                  setEditCommentText(comment.content);
                                }}
                                className="text-xs text-blue-600 hover:text-blue-700"
                              >
                                <Edit3 className="h-3 w-3 inline mr-1" />
                                Edit
                              </button>
                              <button
                                onClick={() => handleDeleteCommentClick(comment.id)}
                                className="text-xs text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-3 w-3 inline mr-1" />
                                Delete
                              </button>
                            </div>
                          )}

                          {/* Reply Button */}
                          <div className="mt-2">
                            <button
                              onClick={() => toggleReplyInput(comment.id)}
                              className="flex items-center space-x-1 text-xs text-gray-500 hover:text-[#2c6f4a] transition-colors"
                            >
                              <MessageCircle className="h-3 w-3" />
                              <span>
                                {showReplyInput[comment.id]
                                  ? "Cancel Reply"
                                  : "Reply"}
                              </span>
                            </button>
                          </div>
                        </>
                      )}

                      {/* Reply Input Section */}
                      {showReplyInput[comment.id] && (
                        <div className="mt-3 space-y-2">
                          <textarea
                            value={replyInputs[comment.id] || ""}
                            onChange={(e) =>
                              setReplyInputs((prev) => ({
                                ...prev,
                                [comment.id]: e.target.value,
                              }))
                            }
                            placeholder="Reply to this comment..."
                            rows={2}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md resize-none text-sm focus:outline-none focus:ring-2 focus:ring-[#2c6f4a]/20 focus:border-[#2c6f4a] text-black placeholder-gray-500"
                          />
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={() => toggleReplyInput(comment.id)}
                              className="text-xs bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600"
                            >
                              Cancel
                            </button>
                            <button
                              onClick={() => {
                                handleNestedReply(comment.id, post.id);
                                setShowReplyInput((prev) => ({
                                  ...prev,
                                  [comment.id]: false,
                                }));
                              }}
                              disabled={replyLoading[comment.id]}
                              className="text-xs bg-[#2c6f4a] text-white px-3 py-1 rounded hover:bg-[#3a8b5c] disabled:opacity-50"
                            >
                              {replyLoading[comment.id]
                                ? "Replying..."
                                : "Reply"}
                            </button>
                          </div>
                        </div>
                      )}

                      {/* Nested Replies */}
                      {comment.replies && comment.replies.length > 0 && (
                        <div className="mt-4 ml-6 space-y-3">
                          {comment.replies.map((reply, replyIndex) => (
                            <div
                              key={
                                reply.id ||
                                `reply-${replyIndex}-${comment.id}-${post.id}`
                              }
                              className="p-3 bg-white border border-green-100 rounded-lg shadow-sm"
                            >
                              <div className="flex items-center space-x-2 mb-1">
                                <div className="w-6 h-6 bg-gradient-to-r from-[#ffc35a] to-[#ffb84d] rounded-full flex items-center justify-center">
                                  <span className="text-[10px] font-medium text-white">
                                    {reply.author
                                      .split(" ")
                                      .map((n: string) => n[0])
                                      .join("")}
                                  </span>
                                </div>
                                <h6 className="font-medium text-xs text-gray-900">
                                  {reply.author}
                                </h6>
                                <span className="text-[10px] text-gray-500">
                                  {reply.authorTitle}
                                </span>
                                <span className="text-[10px] text-gray-400">
                                  • {reply.timeAgo}
                                </span>
                              </div>

                              {editingReply === reply.id ? (
                                <div className="space-y-2">
                                  <textarea
                                    value={editReplyText}
                                    onChange={(e) =>
                                      setEditReplyText(e.target.value)
                                    }
                                    rows={2}
                                    className="w-full px-2 py-1 border border-gray-300 rounded text-xs resize-none focus:outline-none focus:ring-1 focus:ring-[#2c6f4a]/20 focus:border-[#2c6f4a]"
                                  />
                                  <div className="flex space-x-2">
                                    <button
                                      onClick={() => handleReplyEdit(reply.id)}
                                      className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                                    >
                                      Save
                                    </button>
                                    <button
                                      onClick={() => {
                                        setEditingReply(null);
                                        setEditReplyText("");
                                      }}
                                      className="text-xs bg-gray-500 text-white px-2 py-1 rounded hover:bg-gray-600"
                                    >
                                      Cancel
                                    </button>
                                  </div>
                                </div>
                              ) : (
                                <>
                                  <p className="text-xs text-gray-700 mb-2">
                                    {reply.content}
                                  </p>

                                  {/* Reply Edit/Delete Buttons */}
                                  {reply.isUserComment && (
                                    <div className="flex space-x-2 mt-2">
                                      <button
                                        onClick={() => {
                                          setEditingReply(reply.id);
                                          setEditReplyText(reply.content);
                                        }}
                                        className="text-xs text-blue-600 hover:text-blue-700"
                                      >
                                        <Edit3 className="h-3 w-3 inline mr-1" />
                                        Edit
                                      </button>
                                      <button
                                        onClick={() => handleDeleteReplyClick(reply.id)}
                                        className="text-xs text-red-600 hover:text-red-700"
                                      >
                                        <Trash2 className="h-3 w-3 inline mr-1" />
                                        Delete
                                      </button>
                                    </div>
                                  )}
                                </>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              {post.comments.length === 0 && (
                <div className="p-4 text-center text-gray-500 text-sm">
                  No comments yet. Be the first to comment!
                </div>
              )}
            </div>
          </div>
        </div>
      )}
      <ComingSoonModal
        isOpen={comingSoonModalOpen}
        onClose={() => setComingSoonModalOpen(false)}
      />
      {showDeleteCommentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-sm w-full">
            <div className="p-6">
              <h3 className="text-lg font-bold text-center mb-2 text-red-700">Confirm Delete</h3>
              <p className="text-gray-700 mb-6 text-center">Are you sure you want to delete this comment?</p>
              <div className="flex space-x-3">
                <button
                  onClick={async () => {
                    await handleCommentDelete(showDeleteCommentModal);
                    setShowDeleteCommentModal(null);
                  }}
                  className="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-all duration-200 font-medium"
                >
                  Yes
                </button>
                <button
                  onClick={() => setShowDeleteCommentModal(null)}
                  className="flex-1 bg-gray-300 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-400 transition-all duration-200 font-medium"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      {showDeleteReplyModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-sm w-full">
            <div className="p-6">
              <h3 className="text-lg font-bold text-center mb-2 text-red-700">Confirm Delete</h3>
              <p className="text-gray-700 mb-6 text-center">Are you sure you want to delete this reply?</p>
              <div className="flex space-x-3">
                <button
                  onClick={async () => {
                    await handleReplyDelete(showDeleteReplyModal);
                    setShowDeleteReplyModal(null);
                  }}
                  className="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-all duration-200 font-medium"
                >
                  Yes
                </button>
                <button
                  onClick={() => setShowDeleteReplyModal(null)}
                  className="flex-1 bg-gray-300 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-400 transition-all duration-200 font-medium"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
