'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { TranslationService } from '../services/translationService';

// Import all message files
import enMessages from '../../messages/en.json';
import hiMessages from '../../messages/hi.json';
import taMessages from '../../messages/ta.json';
import teMessages from '../../messages/te.json';
import bnMessages from '../../messages/bn.json';
import mrMessages from '../../messages/mr.json';
import guMessages from '../../messages/gu.json';
import knMessages from '../../messages/kn.json';
import mlMessages from '../../messages/ml.json';
import paMessages from '../../messages/pa.json';
import orMessages from '../../messages/or.json';
import asMessages from '../../messages/as.json';

interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
}

const languages: Language[] = [
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
  { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी', flag: '🇮🇳' },
  { code: 'ta', name: 'Tamil', nativeName: 'தமிழ்', flag: '🇮🇳' },
  { code: 'te', name: 'Telugu', nativeName: 'తెలుగు', flag: '🇮🇳' },
  { code: 'bn', name: 'Bengali', nativeName: 'বাংলা', flag: '🇮🇳' },
  { code: 'mr', name: 'Marathi', nativeName: 'मराठी', flag: '🇮🇳' },
  { code: 'gu', name: 'Gujarati', nativeName: 'ગુજરાતી', flag: '🇮🇳' },
  { code: 'kn', name: 'Kannada', nativeName: 'ಕನ್ನಡ', flag: '🇮🇳' },
  { code: 'ml', name: 'Malayalam', nativeName: 'മലയാളം', flag: '🇮🇳' },
  { code: 'pa', name: 'Punjabi', nativeName: 'ਪੰਜਾਬੀ', flag: '🇮🇳' },
  { code: 'or', name: 'Odia', nativeName: 'ଓଡ଼ିଆ', flag: '🇮🇳' },
  { code: 'as', name: 'Assamese', nativeName: 'অসমীয়া', flag: '🇮🇳' },
];

// Messages map
const messagesMap: Record<string, any> = {
  en: enMessages,
  hi: hiMessages,
  ta: taMessages,
  te: teMessages,
  bn: bnMessages,
  mr: mrMessages,
  gu: guMessages,
  kn: knMessages,
  ml: mlMessages,
  pa: paMessages,
  or: orMessages,
  as: asMessages,
};

interface LanguageContextType {
  currentLanguage: Language;
  languages: Language[];
  changeLanguage: (language: Language) => void;
  t: (key: string) => string;
  translateText: (text: string) => string;
  translatePost: (post: any) => any;
  translateNotification: (notification: any) => any;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState<Language>(languages[0]);
  const [messages, setMessages] = useState<any>(messagesMap.en); // Initialize with English

  const changeLanguage = (language: Language) => {
    setCurrentLanguage(language);
    setMessages(messagesMap[language.code] || messagesMap.en);
  };

  const t = (key: string): string => {
    const keys = key.split('.');
    let value = messages;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return key; // Return key if translation not found
      }
    }

    return typeof value === 'string' ? value : key;
  };

  const translateText = (text: string): string => {
    return TranslationService.translateText(text, currentLanguage.code);
  };

  const translatePost = (post: any): any => {
    return TranslationService.translatePost(post, currentLanguage.code);
  };

  const translateNotification = (notification: any): any => {
    return TranslationService.translateNotification(notification, currentLanguage.code);
  };

  const value: LanguageContextType = {
    currentLanguage,
    languages,
    changeLanguage,
    t,
    translateText,
    translatePost,
    translateNotification,
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
