import { useState, useEffect, useCallback } from "react";
import {
  notificationService,
  Notification,
} from "../services/notificationService";

export const useNotifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const calculateUnreadCount = useCallback((notifs: Notification[]) => {
    return notifs.filter((n) => !n.isRead).length;
  }, []);

  const fetchNotifications = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await notificationService.getNotifications();

      if (response.success && response.notifications) {
        setNotifications(response.notifications);
        // Don't update unread count here - let it persist until manually cleared
      } else {
        setError(response.error || "Failed to fetch notifications");
      }
    } catch (err) {
      setError("Failed to fetch notifications");
      console.error("Fetch notifications error:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Calculate unread count separately and update it only when needed
  useEffect(() => {
    const count = calculateUnreadCount(notifications);
    setUnreadCount(count);
  }, [notifications, calculateUnreadCount]);

  // Mark all as read when panel is opened
  const markAllAsReadOnView = useCallback(async () => {
    try {
      const response = await notificationService.markAllAsRead();

      if (response.success) {
        setNotifications((prev) => prev.map((n) => ({ ...n, isRead: true })));
        setUnreadCount(0);
      }
    } catch (err) {
      console.error("Mark all as read on view error:", err);
    }
  }, []);

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const response = await notificationService.markAsRead(notificationId);

      if (response.success) {
        setNotifications((prev) =>
          prev.map((n) =>
            n.id === notificationId ? { ...n, isRead: true } : n
          )
        );
        setUnreadCount((prev) => Math.max(0, prev - 1));
      } else {
        console.error("Failed to mark as read:", response.error);
      }
    } catch (err) {
      console.error("Mark as read error:", err);
    }
  }, []);

  const markAllAsRead = useCallback(async () => {
    try {
      const response = await notificationService.markAllAsRead();

      if (response.success) {
        setNotifications((prev) => prev.map((n) => ({ ...n, isRead: true })));
        setUnreadCount(0);
      }
    } catch (err) {
      console.error("Mark all as read error:", err);
    }
  }, []);

  // Auto-refresh notifications every 60 seconds
  useEffect(() => {
    const interval = setInterval(fetchNotifications, 60000);
    return () => clearInterval(interval);
  }, [fetchNotifications]);

  return {
    notifications,
    unreadCount,
    loading,
    error,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    markAllAsReadOnView,
  };
};
