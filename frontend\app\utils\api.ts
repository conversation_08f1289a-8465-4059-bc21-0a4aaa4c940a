const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "https://buyers-backend-606425615474.asia-south1.run.app";

export interface PaymentData {
  name: string;
  mobile: string;
  email?: string;
  amount: number;
  transactionId?: string;
}

export interface StatusResponse {
  redirect: string;
  status: string;
  data?: {
    merchantId: string;
    merchantTransactionId: string;
    transactionId: string;
    amount: number;
    state: string;
  };
}

export interface PaymentResponse {
  success: boolean;
  code: string;
  message: string;
  data: {
    merchantId: string;
    merchantTransactionId: string;
    instrumentResponse: {
      type: string;
      redirectInfo: {
        url: string;
        method: string;
      };
    };
  };
}

export const paymentAPI = {
  createOrder: async (
    data: PaymentData
  ): Promise<{ data: PaymentResponse }> => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/order`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return { data: result };
    } catch (error) {
      console.error("Payment API error:", error);
      throw error;
    }
  },

  checkStatus: async (
    transactionId: string,
    testMode?: string | null
  ): Promise<{ data: StatusResponse }> => {
    try {
      const params = new URLSearchParams({ id: transactionId });
      if (testMode) params.append("test", testMode);

      const response = await fetch(
        `${API_BASE_URL}/api/status?${params.toString()}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return { data: result };
    } catch (error) {
      console.error("Status check API error:", error);
      throw error;
    }
  },
};
