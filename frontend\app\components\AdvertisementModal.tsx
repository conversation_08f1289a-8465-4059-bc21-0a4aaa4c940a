"use client";

import React from "react";
import { useState } from "react";
import { X } from "lucide-react";
import { feedbackService } from "../services/feedbackService";

interface AdvertisementModalProps {
  isOpen: boolean;
  onClose: () => void;
  slotTitle: string;
}

interface FormData {
  name: string;
  phoneNumber: string;
  email: string;
  city: string;
  businessIndustry: string;
}

export default function AdvertisementModal({
  isOpen,
  onClose,
  slotTitle,
}: AdvertisementModalProps) {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    phoneNumber: "",
    email: "",
    city: "",
    businessIndustry: "",
  });

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ): void => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();

    // Check if user is logged in
    const token = localStorage.getItem("token");
    if (!token) {
      alert("Please log in to book an ad slot");
      return;
    }

    try {
      // Map form data to match feedbackService interface
      const adBookingData: {
        name: string;
        phone: string;
        email: string;
        city: string;
        businessIndustry: string;
      } = {
        name: formData.name,
        phone: formData.phoneNumber, // Map phoneNumber to phone
        email: formData.email,
        city: formData.city,
        businessIndustry: formData.businessIndustry,
      };

      console.log(
        "Submitting ad booking from AdvertisementModal:",
        adBookingData
      );

      const response = await feedbackService.submitAdBooking(adBookingData);

      if (response.success) {
        alert("Thank you for your interest! We will contact you soon.");
        onClose();
        // Reset form
        setFormData({
          name: "",
          phoneNumber: "",
          email: "",
          city: "",
          businessIndustry: "",
        });
      } else {
        alert("Error: " + (response.error || "Failed to book ad slot"));
      }
    } catch (error) {
      console.error("Ad booking error:", error);
      alert("Network error. Please try again.");
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" onClick={onClose}>
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-md mx-auto relative" onClick={e => e.stopPropagation()}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] bg-clip-text text-transparent">
            Book {slotTitle}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 hover:bg-gray-100 p-1 rounded-full transition-all duration-200 z-10"
            style={{ position: 'absolute', top: 8, right: 8 }}
            aria-label="Close"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Name */}
          <div>
            <label
              htmlFor="name"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#2c6f4a]/20 focus:border-[#2c6f4a] text-black"
              required
            />
          </div>

          {/* Phone Number */}
          <div>
            <label
              htmlFor="phoneNumber"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Phone Number *
            </label>
            <input
              type="tel"
              id="phoneNumber"
              name="phoneNumber"
              value={formData.phoneNumber}
              onChange={handleInputChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#2c6f4a]/20 focus:border-[#2c6f4a] text-black"
              required
            />
          </div>

          {/* Email */}
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Email ID *
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#2c6f4a]/20 focus:border-[#2c6f4a] text-black"
              required
            />
          </div>

          {/* City */}
          <div>
            <label
              htmlFor="city"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              City *
            </label>
            <input
              type="text"
              id="city"
              name="city"
              value={formData.city}
              onChange={handleInputChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#2c6f4a]/20 focus:border-[#2c6f4a] text-black"
              required
            />
          </div>

          {/* Business Industry */}
          <div>
            <label
              htmlFor="businessIndustry"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Business Industry *
            </label>
            <select
              id="businessIndustry"
              name="businessIndustry"
              value={formData.businessIndustry}
              onChange={handleInputChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#2c6f4a]/20 focus:border-[#2c6f4a] text-black"
              required
            >
              <option value="">Select your industry</option>
              <option value="Real Estate">Real Estate</option>
              <option value="Construction">Construction</option>
              <option value="Finance">Finance</option>
              <option value="Insurance">Insurance</option>
              <option value="Legal Services">Legal Services</option>
              <option value="Property Management">Property Management</option>
              <option value="Architecture">Architecture</option>
              <option value="Interior Design">Interior Design</option>
              <option value="Home Services">Home Services</option>
              <option value="Technology">Technology</option>
              <option value="Other">Other</option>
            </select>
          </div>

          {/* Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-all duration-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] text-white rounded-md hover:from-[#3a8b5c] hover:to-[#48a76e] transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
