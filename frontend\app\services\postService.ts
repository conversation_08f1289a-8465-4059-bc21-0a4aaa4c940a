import { apiService } from "./apiService";

export interface BackendQuestion {
  id: string;
  title: string;
  body: string;
  userId: string;
  userName?: string;
  userEmail: string; // New field from API
  userRole: string; // New field from API
  userCity: string; // New field from API
  createdAt: string;
  replies: BackendReply[];
  votes: Array<{ type: "UPVOTE" | "DOWNVOTE"; userId: string }>;
  repliesCount: number;
  user?: {
    id: string;
    email: string;
    role: string;
    city: string;
  };
}

export interface BackendReply {
  id: string;
  content: string;
  userId: string;
  createdAt: string;
  parentReplyId?: string;
  user: {
    email: string;
    role: string;
    city: string;
  };
  childReplies: BackendReply[];
}

export interface Comment {
  id: string;
  author: string;
  authorTitle: string;
  authorCity: string;
  timeAgo: string;
  content: string;
  upvotes: number;
  hasUpvoted: boolean;
  replies: Comment[];
  userId: string;
  isUserComment: boolean;
}

// Transform backend data to frontend format
function transformQuestion(
  backendQuestion: BackendQuestion,
  isBookmarked: boolean = false
): Post {
  const currentUser = JSON.parse(localStorage.getItem("user") || "{}");
  const userVote = backendQuestion.votes?.find(
    (v) => v.userId === currentUser.id
  );

  // Remove userObj and only use direct properties from backendQuestion
  const authorEmail = backendQuestion.userEmail || backendQuestion.user?.email;
  const authorRole = backendQuestion.userRole || backendQuestion.user?.role;
  const authorCity = backendQuestion.userCity || backendQuestion.user?.city;

  const authorName =
    authorEmail?.split("@")[0] ||
    backendQuestion.userName ||
    "Anonymous";

  return {
    id: backendQuestion.id,
    author: authorName,
    authorTitle: mapRoleToTitle(authorRole || "BUYER"),
    authorCity: authorCity || "Unknown",
    timeAgo: formatTimeAgo(backendQuestion.createdAt),
    question: backendQuestion.title,
    content: backendQuestion.body,
    upvotes:
      backendQuestion.votes?.filter((v) => v.type === "UPVOTE").length || 0,
    downvotes:
      backendQuestion.votes?.filter((v) => v.type === "DOWNVOTE").length || 0,
    comments: transformReplies(backendQuestion.replies || []),
    repliesCount: backendQuestion.repliesCount,
    hasUpvoted: userVote?.type === "UPVOTE",
    hasDownvoted: userVote?.type === "DOWNVOTE",
    isBookmarked,
    isUserPost: backendQuestion.userId === currentUser.id,
    userId: backendQuestion.userId,
    createdAt: backendQuestion.createdAt,
  };
}

function transformReplies(replies: BackendReply[]): Comment[] {
  const replyMap: { [id: string]: Comment } = {};

  // First pass: create all reply objects
  replies.forEach((reply) => {
    const user = reply.user || {
      email: "<EMAIL>",
      role: "Community Member",
      city: "Unknown",
    };

    const currentUser = JSON.parse(localStorage.getItem("user") || "{}");

    replyMap[reply.id] = {
      id: reply.id,
      author: user.email?.split("@")[0] || "Anonymous",
      authorTitle: mapRoleToTitle(user.role),
      authorCity: user.city || "",
      timeAgo: formatTimeAgo(reply.createdAt),
      content: reply.content,
      upvotes: 0,
      hasUpvoted: false,
      replies: [],
      userId: reply.userId,
      isUserComment: reply.userId === currentUser.id, // ✅ ADD THIS LINE
    };
  });

  const topLevelReplies: Comment[] = [];

  // Second pass: build parent-child relationships
  replies.forEach((reply) => {
    if (reply.parentReplyId) {
      const parent = replyMap[reply.parentReplyId];
      if (parent) {
        parent.replies.push(replyMap[reply.id]);
      }
    } else {
      topLevelReplies.push(replyMap[reply.id]);
    }
  });

  return topLevelReplies;
}

function formatTimeAgo(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMinutes = Math.floor(
    (now.getTime() - date.getTime()) / (1000 * 60)
  );

  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
  return `${Math.floor(diffInMinutes / 1440)}d ago`;
}
export interface Post {
  id: string;
  question: string;
  content: string;
  author: string;
  authorTitle: string;
  authorCity: string;

  userEmail?: string;
  userRole?: string;
  userCity?: string;

  timeAgo: string;
  upvotes: number;
  downvotes: number;
  hasUpvoted: boolean;
  hasDownvoted: boolean;
  isBookmarked: boolean;
  isUserPost: boolean;
  userId: string;
  createdAt: string;
  comments: Comment[];
  commentCount?: number;
  repliesCount: number;
}

export interface PostsResponse {
  success: boolean;
  data?: Post[];
  error?: string;
}
export interface PostResponse {
  success: boolean;
  data?: Post;
  error?: string;
}
export interface CreatePostRequest {
  title: string;
  content: string;
}

export const postService = {
  async getPosts(): Promise<PostsResponse> {
    const response = await apiService.get<BackendQuestion[]>("/api/questions");

    if (!response.success) {
      return { success: false, error: response.error };
    }

    // Get user's bookmarks to check which posts are bookmarked
    const bookmarksResponse = await apiService.get("/api/bookmarks");

    let userBookmarks: Array<{ id: string } & Record<string, any>> = [];
    if (bookmarksResponse.success) {
      if (Array.isArray(bookmarksResponse.data)) {
        userBookmarks = bookmarksResponse.data;
      } else if (
        bookmarksResponse.data &&
        typeof bookmarksResponse.data === 'object' &&
        Array.isArray((bookmarksResponse.data as any).bookmarks)
      ) {
        userBookmarks = (bookmarksResponse.data as any).bookmarks;
      }
    }

    console.log("User bookmarks:", userBookmarks); // Debug log

    const bookmarkedIds = new Set(
      (userBookmarks as Record<string, unknown>[])
        .filter((bookmark) => bookmark && typeof (bookmark as any).id === 'string')
        .map((bookmark) => (bookmark as any).id)
    );

    console.log("Bookmarked IDs:", Array.from(bookmarkedIds)); // Debug log

    let transformedPosts: Post[] = [];
    if (Array.isArray(response.data)) {
      transformedPosts = response.data.map((question: BackendQuestion) => {
        const isBookmarked = bookmarkedIds.has(question.id);
        console.log(`Post ${question.id} is bookmarked:`, isBookmarked); // Debug log
        return transformQuestion(question, isBookmarked);
      });
    }

    return { success: true, data: transformedPosts };
  },

  async createPost(postData: CreatePostRequest): Promise<PostResponse> {
    const response = await apiService.post("/api/questions", {
      title: postData.title,
      body: postData.content,
    });

    if (!response.success) {
      return { success: false, error: response.error };
    }

    // Get current user data from localStorage
    const currentUser = JSON.parse(localStorage.getItem("user") || "{}");

    // Create post object with proper user data from localStorage
    const tempPost: Post = {
      id: (response.data as any)?.id || Date.now().toString(),
      author:
        currentUser.name || currentUser.email?.split("@")?.[0] || "Anonymous",
      authorTitle: mapRoleToTitle(currentUser.role || currentUser.interest),
      authorCity: currentUser.city || "Unknown",
      timeAgo: "now",
      question: postData.title,
      content: postData.content,
      upvotes: 0,
      downvotes: 0,
      comments: [],
      commentCount: 0,
      hasUpvoted: false,
      hasDownvoted: false,
      isBookmarked: false,
      isUserPost: true,
      userId: currentUser.id || "",
      createdAt: new Date().toISOString(),
      repliesCount: 0,
    };

    return { success: true, data: tempPost };
  },

  async updatePost(
    postId: string,
    updateData: { title: string; content: string }
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await apiService.put(`/api/questions/${postId}`, {
        title: updateData.title,
        body: updateData.content,
      });

      return { success: response.success, error: response.error };
    } catch (error) {
      console.error("Error updating post:", error);
      return { success: false, error: "Failed to update post" };
    }
  },

  async deletePost(
    postId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await apiService.delete(`/api/questions/${postId}`);
      return { success: response.success, error: response.error };
    } catch (error) {
      console.error("Error deleting post:", error);
      return { success: false, error: "Failed to delete post" };
    }
  },

  addReplyToComment: async (
    commentId: string,
    content: string,
    userId: string,
    postId: string // 👈 you must pass this too!
  ): Promise<{ success: boolean; data?: any; error?: string }> => {
    try {
      const response = await apiService.post(
        `/api/replies/${commentId}/reply`,
        {
          content,
          userId,
          questionId: postId, // ✅ required by backend
        }
      );

      if (!response.success) {
        return { success: false, error: response.error };
      }

      return { success: true, data: response.data };
    } catch (error) {
      console.error("Error posting nested reply:", error);
      return { success: false, error: "Nested reply failed" };
    }
  },

  async updatePostVote(
    postId: string,
    voteType: "upvote" | "downvote"
  ): Promise<{ success: boolean; error?: string }> {
    const response = await apiService.post(`/api/questions/${postId}/vote`, {
      type: voteType.toUpperCase(),
    });

    return { success: response.success, error: response.error };
  }, // ✅ Required comma here

  addComment: async (
    postId: string,
    content: string
  ): Promise<{ success: boolean; data?: Comment; error?: string }> => {
    const currentUser = JSON.parse(localStorage.getItem("user") || "{}");

    const response = await apiService.post(`/api/questions/${postId}/reply`, {
      content,
      userId: currentUser.id,
    });

    if (!response.success) {
      return { success: false, error: response.error };
    }

    const transformedReply = transformReplies([response.data as BackendReply])[0];

    return { success: true, data: transformedReply };
  },

  async getShareLinks(
    postId: string
  ): Promise<{ success: boolean; links?: any; error?: string }> {
    const response = await apiService.get(`/api/questions/${postId}/share`);
    return {
      success: response.success,
      links: response.data && (response.data as any).links,
      error: response.error,
    };
  },

  async shareToSpecificPlatform(
    postId: string,
    platform: string
  ): Promise<{ success: boolean; link?: string; error?: string }> {
    const response = await apiService.get(
      `/api/questions/${postId}/share?platform=${platform}`
    );
    return {
      success: response.success,
      link: response.data && (response.data as any).link,
      error: response.error,
    };
  },

  async getAIResponse(
    postId: string,
    question: string
  ): Promise<{ success: boolean; response?: string; error?: string }> {
    // Keep the existing AI response simulation since it's not in the backend API
    await new Promise((resolve) => setTimeout(resolve, 2000));
    return {
      success: true,
      response: `Based on your question about "${question}", here are some key insights: This is a simulated AI response for demonstration purposes.`,
    };
  },

  async updateComment(
    commentId: string,
    newContent: string
  ): Promise<{ success: boolean; error?: string }> {
    const response = await apiService.put(`/api/replies/${commentId}`, {
      content: newContent,
    });
    return { success: response.success, error: response.error };
  },

  async deleteComment(
    commentId: string
  ): Promise<{ success: boolean; error?: string }> {
    const response = await apiService.delete(`/api/replies/${commentId}`);
    return { success: response.success, error: response.error };
  },

  async deleteReply(
    replyId: string
  ): Promise<{ success: boolean; error?: string }> {
    const response = await apiService.delete(`/api/replies/${replyId}`);
    return { success: response.success, error: response.error };
  },

  editComment: async (commentId: string, data: { content: string }) => {
    try {
      const res = await apiService.put(`/api/replies/${commentId}`, data);
      return res;
    } catch (error) {
      console.error("Failed to edit comment:", error);
      return { success: false, error };
    }
  },

  async searchPosts(query: string): Promise<PostsResponse> {
    const response = await apiService.get(
      `/api/search?keyword=${encodeURIComponent(query)}`
    );

    if (!response.success) {
      return { success: false, error: response.error };
    }

    let postsArray: BackendQuestion[] = [];
    if (response.data && Array.isArray((response.data as any).data)) {
      postsArray = (response.data as any).data;
    }
    if (!Array.isArray(postsArray)) {
      console.warn("Expected array but got:", postsArray);
      return { success: false, error: "Invalid search response" };
    }
    const transformedSearchPosts = postsArray.map((q: BackendQuestion) => transformQuestion(q));
    return { success: true, data: transformedSearchPosts };
  },

  getBookmarks: async (): Promise<PostsResponse> => {
    try {
      const response = await apiService.get(`/api/bookmarks`);

      console.log("Bookmarks API response:", response); // Debug log

      if (!response.success) {
        return { success: false, error: response.error };
      }

      // Handle different response structures
      let bookmarks = [];
      if (Array.isArray(response.data)) {
        bookmarks = response.data;
      } else if (
        response.data &&
        typeof response.data === 'object' &&
        Array.isArray((response.data as any).bookmarks)
      ) {
        bookmarks = (response.data as any).bookmarks;
      }

      console.log("Extracted bookmarks:", bookmarks); // Debug log

      if (!Array.isArray(bookmarks)) {
        console.warn("Bookmarks is not an array:", bookmarks);
        return { success: false, error: "Invalid bookmarks response" };
      }

      const transformedPosts = bookmarks.map((bookmark: BackendQuestion) =>
        transformQuestion(bookmark, true)
      );

      console.log("Transformed posts:", transformedPosts); // Debug log

      return { success: true, data: transformedPosts };
    } catch (error) {
      console.error("Failed to fetch bookmarks:", error);
      return { success: false, error: "Failed to fetch bookmarks" };
    }
  },

  saveBookmark: async (
    postId: string
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      console.log("Attempting to save bookmark for post:", postId);
      const res = await apiService.post(`/api/bookmarks/${postId}`);
      console.log("Bookmark API response:", res);

      // Check if it's already bookmarked (which might return an error but is actually success)
      if (
        !res.success &&
        (res.error === "Request failed" || res.error?.includes("already"))
      ) {
        console.log("Checking if post is already bookmarked...");
        const isBookmarked = await postService.isPostBookmarked(postId);
        if (isBookmarked) {
          console.log("Post is already bookmarked, treating as success");
          return { success: true }; // Already bookmarked, so operation succeeded
        }
      }

      return { success: res.success, error: res.error };
    } catch (error) {
      console.error("Failed to save bookmark:", error);
      return { success: false, error: "Save bookmark failed" };
    }
  },

  removeBookmark: async (
    postId: string
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      const res = await apiService.delete(`/api/bookmarks/${postId}`);
      // Even if we get a 400 because it's not bookmarked, consider it a success
      if (!res.success && res.error === "Request failed") {
        // Check if it's already not bookmarked
        const isBookmarked = await postService.isPostBookmarked(postId);
        if (!isBookmarked) {
          return { success: true }; // Already not bookmarked, so operation succeeded
        }
      }
      return { success: res.success, error: res.error };
    } catch (error) {
      console.error("Failed to remove bookmark:", error);
      return { success: false, error: "Remove bookmark failed" };
    }
  },

  async isPostBookmarked(postId: string): Promise<boolean> {
    try {
      const response = await apiService.get("/api/bookmarks");
      if (!response.success) return false;

      let bookmarks = [];
      if (Array.isArray(response.data)) {
        bookmarks = response.data;
      } else if (
        response.data &&
        typeof response.data === 'object' &&
        Array.isArray((response.data as any).bookmarks)
      ) {
        bookmarks = (response.data as any).bookmarks;
      }

      console.log("Checking bookmark for post:", postId);
      console.log("Available bookmarks:", bookmarks);

      const isBookmarked = bookmarks.some(
        (bookmark: any) => bookmark.id === postId
      );
      console.log("Is bookmarked:", isBookmarked);

      return isBookmarked;
    } catch (error) {
      console.error("Error checking bookmark status:", error);
      return false;
    }
  },
};

// Helper function to map backend roles to display titles
function mapRoleToTitle(role: string): string {
  const roleMap: Record<string, string> = {
    BUYER: "Buyer",
    AGENT: "Agent",
    DEVELOPER: "Developer",
    Buyer: "Buyer",
    Agent: "Agent",
    Developer: "Developer",
    Explorer: "Explorer",
  };

  return roleMap[role] || "Community Member";
}
