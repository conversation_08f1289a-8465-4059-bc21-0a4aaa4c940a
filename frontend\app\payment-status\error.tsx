'use client';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h2 className="text-xl font-bold text-red-600">Something went wrong!</h2>
        <p className="mt-2 text-gray-600">Payment status check failed</p>
        <button
          onClick={() => window.location.href = '/failure'}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded"
        >
          Go to Failure Page
        </button>
      </div>
    </div>
  );
}