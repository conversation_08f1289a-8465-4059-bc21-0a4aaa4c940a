"use client";

import { Suspense } from "react";
import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";

function PaymentStatusContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [status, setStatus] = useState("checking");
  const [error, setError] = useState("");
  const transactionId = searchParams.get("id");

  useEffect(() => {
    console.log("PaymentStatus mounted with transactionId:", transactionId);

    if (!transactionId) {
      console.log("No transaction ID found, redirecting to failure");
      router.push("/payment-failure");
      return;
    }

    const timer = setTimeout(() => {
      console.log("Starting status check after 2 second delay");
      const backendUrl =
        process.env.NEXT_PUBLIC_API_URL || "https://buyers-backend-606425615474.asia-south1.run.app";
      const statusUrl = `${backendUrl}/api/status?id=${transactionId}`;

      console.log("Checking status at:", statusUrl);
      setStatus("fetching");

      fetch(statusUrl)
        .then((res) => {
          console.log("Status check response status:", res.status);

          if (!res.ok) {
            throw new Error(`HTTP ${res.status}: ${res.statusText}`);
          }
          return res.json();
        })
        .then((data) => {
          console.log("=== PAYMENT STATUS DEBUG ===");
          console.log("Full response:", JSON.stringify(data, null, 2));
          console.log("Status value:", data.status);
          console.log("Redirect URL:", data.redirect);

          if (data.redirect) {
            console.log("🔄 Using backend redirect URL:", data.redirect);
            router.push(data.redirect);
          } else if (data.status === "COMPLETED") {
            console.log("✅ COMPLETED DETECTED - Redirecting to success");
            router.push(`/payment-success?id=${transactionId}`);
          } else if (data.status === "FAILED") {
            console.log("❌ FAILED DETECTED - Redirecting to failure");
            router.push("/payment-failure");
          } else if (data.status === "PENDING") {
            console.log("⏳ PENDING DETECTED - Redirecting to pending");
            router.push(`/payment-pending?id=${transactionId}`);
          } else {
            console.log("❓ UNKNOWN STATUS - redirecting to failure");
            router.push("/payment-failure");
          }
        })
        .catch((error) => {
          console.error("Status check failed:", error);
          setError(error.message);
          setStatus("error");
          router.push("/payment-failure");
        });
    }, 2000);

    return () => clearTimeout(timer);
  }, [transactionId, router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center px-4">
      <div className="bg-white rounded-2xl shadow-xl border border-slate-200 p-8 max-w-md w-full text-center">
        <div className="mb-6">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-slate-200 border-t-blue-600 mx-auto"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-8 h-8 bg-blue-600 rounded-full opacity-20 animate-pulse"></div>
            </div>
          </div>
        </div>

        <h2 className="text-2xl font-bold text-slate-800 mb-3">
          Verifying Payment
        </h2>

        <p className="text-slate-600 mb-6 leading-relaxed">
          Please wait while we securely verify your payment with our banking
          partner...
        </p>

        {transactionId && (
          <div className="bg-slate-50 rounded-lg p-4 mb-4">
            <p className="text-sm text-slate-500 font-medium">Transaction ID</p>
            <p className="text-slate-700 font-mono text-sm mt-1">
              {transactionId}
            </p>
          </div>
        )}

        <div className="flex items-center justify-center text-xs text-slate-400">
          <span className="mr-2">🔒</span>
          Secured by 256-bit SSL encryption
        </div>
      </div>
    </div>
  );
}

export default function PaymentStatus() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PaymentStatusContent />
    </Suspense>
  );
}
