"use client";
import { useEffect, useState } from "react";
import { Users, MapPin, Building, UserCheck } from "lucide-react";
import { useLanguage } from "../contexts/LanguageContext";

interface CityData {
  name: string;
  count: number;
  flag: string;
}

interface UserTypeData {
  type: string;
  count: number;
  icon: any;
  color: string;
}

export default function NetworkDemographics() {
  const { t } = useLanguage();

  const [cityData, setCityData] = useState<CityData[]>([]);
  const [userTypeData, setUserTypeData] = useState<UserTypeData[]>([]);

  useEffect(() => {
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || "https://buyers-backend-606425615474.asia-south1.run.app";
    fetch(`${backendUrl}/api/network-demographics`)
      .then((res) => res.json())
      .then((data) => {
        const cityFlags = {
          Delhi: "🇮🇳",
          Pune: "🇮🇳",
          Bengaluru: "🇮🇳",
          Hyderabad: "🇮🇳",
          Noida: "🇮🇳",
          "Greater Noida": "🇮🇳",
          Germany: "🇩🇪",
          Dubai: "🇦🇪",
        };

        const updatedCities = data.cities.map((city: any) => ({
          name: city.name,
          count: city.count,
          flag: cityFlags[city.name as keyof typeof cityFlags] || "🏙️",
        }));

        const userTypesIconAndColor = {
          Buyer: { icon: Users, color: "from-[#2c6f4a] to-[#3a8b5c]" },
          Agent: { icon: UserCheck, color: "from-[#ffc35a] to-[#ffb84d]" },
          Developer: { icon: Building, color: "from-[#2c64fa] to-[#4a7cfc]" },
        };

        const updatedUserTypes = data.userTypes.map((user: any) => ({
          type: user.type,
          count: user.count,
          icon: userTypesIconAndColor[user.type as keyof typeof userTypesIconAndColor]?.icon || Users,
          color:
            userTypesIconAndColor[user.type as keyof typeof userTypesIconAndColor]?.color ||
            "from-gray-300 to-gray-400",
        }));

        setCityData(updatedCities);
        setUserTypeData(updatedUserTypes);
      });
  }, []);

  const totalUsers = userTypeData.reduce((sum, type) => sum + type.count, 0);

  return (
    <div className="w-full space-y-4">
      <div className="bg-gradient-to-br from-white to-green-50/50 rounded-lg shadow-lg border border-green-200/50 backdrop-blur-sm">
        <div className="p-4">
          <div className="flex items-center space-x-2 mb-4">
            <MapPin className="h-5 w-5 text-[#2c6f4a]" />
            <h3 className="text-sm font-semibold bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] bg-clip-text text-transparent">
              {t("demographics.title")}
            </h3>
          </div>

          <div className="mb-4">
            <h4 className="text-xs font-medium text-gray-600 mb-3 uppercase tracking-wide">
              {t("demographics.topCities")}
            </h4>
            <div className="space-y-2">
              {cityData.map((city, index) => (
                <div
                  key={city.name}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-sm">{city.flag}</span>
                    <span className="text-sm text-gray-700 font-medium">
                      {city.name}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-semibold bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] bg-clip-text text-transparent">
                      {city.count}
                    </span>
                    <div
                      className={`w-1 h-4 rounded-full bg-gradient-to-b from-[#2c6f4a] to-[#3a8b5c]`}
                      style={{ opacity: 1 - index * 0.15 }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h4 className="text-xs font-medium text-gray-600 mb-3 uppercase tracking-wide">
              {t("demographics.userTypes")}
            </h4>
            <div className="space-y-3">
              {userTypeData.map((userType) => {
                const percentage = totalUsers
                  ? ((userType.count / totalUsers) * 100).toFixed(1)
                  : 0;
                const IconComponent = userType.icon;

                return (
                  <div key={userType.type} className="space-y-1">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div
                          className={`p-1 rounded-full bg-gradient-to-r ${userType.color}`}
                        >
                          <IconComponent className="h-3 w-3 text-white" />
                        </div>
                        <span className="text-xs text-gray-700 font-medium">
                          {userType.type}
                        </span>
                      </div>
                      <span className="text-xs font-semibold text-gray-800">
                        {userType.count}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div
                        className={`h-1.5 rounded-full bg-gradient-to-r ${userType.color}`}
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          <div className="mt-4 pt-3 border-t border-green-200/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-[#2c6f4a]" />
                <span className="text-sm font-medium text-gray-700">
                  {t("demographics.totalNetwork")}
                </span>
              </div>
              <span className="text-sm font-bold bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] bg-clip-text text-transparent">
                {totalUsers.toLocaleString()}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
