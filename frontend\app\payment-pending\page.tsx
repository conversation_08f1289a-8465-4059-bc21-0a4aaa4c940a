"use client";

import { Suspense } from "react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";

function PaymentPendingContent() {
  const searchParams = useSearchParams();
  const transactionId = searchParams.get("id");

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 to-yellow-50 flex items-center justify-center px-4">
      <div className="bg-white rounded-2xl shadow-xl border border-amber-200 p-8 max-w-md w-full text-center">
        <div className="mb-6">
          <div className="w-20 h-20 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
            <svg
              className="w-10 h-10 text-white animate-spin"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6l4 2"
              />
              <circle cx="12" cy="12" r="10" strokeWidth={2} />
            </svg>
          </div>
        </div>

        <h2 className="text-3xl font-bold text-amber-700 mb-3">
          Payment Pending
        </h2>

        <p className="text-slate-600 mb-6 leading-relaxed">
          Your payment is being processed by the bank. This usually takes a few
          minutes to complete.
        </p>

        {transactionId && (
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
            <p className="text-sm text-amber-600 font-semibold mb-1">
              Processing Transaction
            </p>
            <p className="text-amber-700 font-mono text-sm">{transactionId}</p>
          </div>
        )}

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="text-sm font-semibold text-blue-700 mb-2">
            What happens next?
          </h3>
          <ul className="text-xs text-blue-600 text-left space-y-1">
            <li>• Bank processes your payment</li>
            <li>• You'll receive SMS/email confirmation</li>
            <li>• Page will auto-update on completion</li>
            <li>• No need to refresh manually</li>
          </ul>
        </div>

        <div className="space-y-3">
          <Link
            href="/"
            className="block w-full bg-gradient-to-r from-amber-600 to-yellow-600 text-white py-4 px-6 rounded-lg font-semibold hover:from-amber-700 hover:to-yellow-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            Go to Dashboard
          </Link>

          <p className="text-xs text-slate-500 mt-4">
            ⏱️ Please don't close this window until payment is confirmed
          </p>
        </div>
      </div>
    </div>
  );
}

export default function PaymentPending() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PaymentPendingContent />
    </Suspense>
  );
}
