"use client";

import { useState } from "react";
import Header from "./components/Header";
import MainFeed from "./components/MainFeed";
import RightSidebar from "./components/RightSidebar";
import SearchSection from "./components/SearchSection";
import NetworkDemographics from "./components/NetworkDemographics";

export default function Home() {
  const [searchTerm, setSearchTerm] = useState("");

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-yellow-50/30">
      <Header searchTerm={searchTerm} setSearchTerm={setSearchTerm} />
      <div className="max-w-7xl mx-auto px-2 sm:px-4 py-4 sm:py-6">
        <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
          {/* Left Sidebar */}
          <div className="hidden md:block md:col-span-3 lg:col-span-2">
            <div className="sticky top-24 space-y-4">
              <SearchSection
                searchTerm={searchTerm}
                setSearchTerm={setSearchTerm}
              />
              <NetworkDemographics />
            </div>
          </div>

          {/* Main Feed */}
          <div className="col-span-1 md:col-span-7 lg:col-span-7">
            <MainFeed searchKeyword={searchTerm} />
          </div>

          {/* Right Sidebar */}
          <div className="hidden md:block md:col-span-2 lg:col-span-3">
            <RightSidebar />
          </div>
        </div>
      </div>
    </div>
  );
}
