"use client";

import { useState, useEffect } from "react";
import { useAuth } from "../contexts/AuthContext";
import { useLanguage } from "../contexts/LanguageContext";
import {
  User,
  FileText,
  ThumbsUp,
  MessageCircle,
  Bookmark,
  MapPin,
  Calendar,
  Briefcase,
  Trash2,
  Edit3,
} from "lucide-react";
import Header from "../components/Header";
import { apiService } from "../services/apiService";
import { postService } from "../services/postService";

interface UserStats {
  totalPosts: number;
  totalVotes: number;
  totalComments: number;
  totalBookmarks: number;
}

interface Post {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  upvotes: number;
  downvotes: number;
  comments: number;
}

interface Vote {
  id: string;
  type: "UPVOTE" | "DOWNVOTE";
  createdAt: string;
  question: {
    id: string;
    title: string;
  };
}

interface Comment {
  id: string;
  content: string;
  createdAt: string;
  question: {
    id: string;
    title: string;
  };
  isReply: boolean;
}

export default function ProfilePage() {
  const { user } = useAuth();
  const { t, translatePost } = useLanguage();
  const [activeTab, setActiveTab] = useState("overview");
  const [userStats, setUserStats] = useState<UserStats>({
    totalPosts: 0,
    totalVotes: 0,
    totalComments: 0,
    totalBookmarks: 0,
  });
  const [userPosts, setUserPosts] = useState<Post[]>([]);
  const [userVotes, setUserVotes] = useState<Vote[]>([]);
  const [userComments, setUserComments] = useState<Comment[]>([]);
  const [bookmarkedPosts, setBookmarkedPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  const handleDeletePost = async (postId: string) => {
    // Prevent duplicate requests
    if (isDeleting === postId) return;

    try {
      setIsDeleting(postId);

      const token = localStorage.getItem("token");
      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_API_URL || "https://buyers-backend-606425615474.asia-south1.run.app"
        }/api/questions/${postId}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        // ✅ Remove from UI state immediately
        setUserPosts((prev) => prev.filter((post) => post.id !== postId));

        // Update stats
        setUserStats((prev) => ({
          ...prev,
          totalPosts: prev.totalPosts - 1,
        }));

        console.log("Question deleted successfully");
      } else {
        throw new Error("Delete failed");
      }
    } catch (error) {
      console.error("Delete error:", error);
      alert("Failed to delete question. Please try again.");
    } finally {
      setIsDeleting(null);
    }
  };

  useEffect(() => {
    fetchUserData();
  }, []);

  const fetchUserData = async () => {
    try {
      setLoading(true);

      // Get current user from localStorage
      const currentUser = JSON.parse(localStorage.getItem("user") || "{}");
      console.log("Current user:", currentUser);

      // Fetch all posts and filter for user's posts
      const allPostsResponse = await postService.getPosts();
      if (allPostsResponse.success) {
        const userPosts = (allPostsResponse.data ?? []).filter(
          (post) => post.userId === currentUser.id
        );
        console.log("User posts found:", userPosts.length);

        // Transform to match Post interface
        const transformedUserPosts = userPosts.map((post) => ({
          id: post.id,
          title: post.question,
          content: post.content,
          createdAt: post.createdAt,
          upvotes: post.upvotes,
          downvotes: post.downvotes,
          comments: post.comments.length,
        }));

        setUserPosts(transformedUserPosts);

        // Extract user votes from all posts
        const userVotesData: Vote[] = [];
        (allPostsResponse.data ?? []).forEach((post) => {
          if (post.hasUpvoted) {
            userVotesData.push({
              id: `vote-${post.id}-up`,
              type: "UPVOTE",
              createdAt: post.createdAt,
              question: {
                id: post.id,
                title: post.question,
              },
            });
          }
          if (post.hasDownvoted) {
            userVotesData.push({
              id: `vote-${post.id}-down`,
              type: "DOWNVOTE",
              createdAt: post.createdAt,
              question: {
                id: post.id,
                title: post.question,
              },
            });
          }
        });
        console.log("User votes found:", userVotesData.length);
        setUserVotes(userVotesData);

        // Extract user comments from all posts
        const userCommentsData: Comment[] = [];
        (allPostsResponse.data ?? []).forEach((post) => {
          (post.comments ?? []).forEach((comment) => {
            if (comment.userId === currentUser.id) {
              userCommentsData.push({
                id: comment.id,
                content: comment.content,
                createdAt: comment.timeAgo,
                question: {
                  id: post.id,
                  title: post.question,
                },
                isReply: false,
              });
            }

            // Check replies too
            if (comment.replies) {
              (comment.replies ?? []).forEach((reply) => {
                if (reply.userId === currentUser.id) {
                  userCommentsData.push({
                    id: reply.id,
                    content: reply.content,
                    createdAt: reply.timeAgo,
                    question: {
                      id: post.id,
                      title: post.question,
                    },
                    isReply: true,
                  });
                }
              });
            }
          });
        });
        console.log("User comments found:", userCommentsData.length);
        setUserComments(userCommentsData);

        // Calculate stats from available data
        setUserStats({
          totalPosts: userPosts.length,
          totalVotes: userVotesData.length,
          totalComments: userCommentsData.length,
          totalBookmarks: 0,
        });
      }

      // Fetch bookmarks
      const bookmarksResponse = await postService.getBookmarks();
      console.log("Bookmarks response:", bookmarksResponse);
      if (bookmarksResponse.success) {
        // Map to local Post type if needed
        const mappedBookmarks = (bookmarksResponse.data ?? []).map((post: any) => ({
          id: post.id,
          title: post.question || post.title || "",
          content: post.content,
          createdAt: post.createdAt,
          upvotes: post.upvotes,
          downvotes: post.downvotes,
          comments: Array.isArray(post.comments) ? post.comments.length : post.comments,
        }));
        setBookmarkedPosts(mappedBookmarks);
        setUserStats((prev) => ({
          ...prev,
          totalBookmarks: mappedBookmarks.length,
        }));
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
    } finally {
      setLoading(false);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  const tabs = [
    { id: "overview", label: "Overview", icon: User },
    { id: "posts", label: "My Posts", icon: FileText },
    { id: "votes", label: "My Votes", icon: ThumbsUp },
    { id: "comments", label: "Comments & Replies", icon: MessageCircle },
    { id: "bookmarks", label: "Bookmarks", icon: Bookmark },
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header searchTerm={""} setSearchTerm={() => {}} />
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2c6f4a]"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-yellow-50/30">
      <Header searchTerm={""} setSearchTerm={() => {}} />

      <div className="max-w-6xl mx-auto px-2 sm:px-6 md:px-8 py-4 sm:py-8">
        {/* Profile Header */}
        <div className="bg-gradient-to-br from-white to-green-50/30 rounded-2xl shadow-xl p-4 sm:p-8 mb-6 sm:mb-10 border border-green-200/50">
          <div className="flex flex-col md:flex-row items-center md:items-start gap-4 md:gap-10 text-center md:text-left">
            <div className="relative flex-shrink-0 flex justify-center md:justify-start">
              <div className="w-20 h-20 sm:w-32 sm:h-32 md:w-40 md:h-40 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] rounded-full flex items-center justify-center shadow-2xl ring-4 ring-white">
                <span className="text-2xl sm:text-4xl md:text-5xl font-bold text-white">
                  {getInitials(user?.name || "")}
                </span>
              </div>
              <div className="absolute -bottom-2 right-1/2 translate-x-1/2 md:translate-x-0 md:right-2 w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-[#ffc35a] to-[#ffb84d] rounded-full flex items-center justify-center shadow-lg">
                <span className="text-white text-xs sm:text-sm">✓</span>
              </div>
            </div>

            <div className="flex-1 md:pl-8 md:max-w-3xl md:text-left">
              <h1 className="text-2xl sm:text-4xl md:text-5xl font-bold bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] bg-clip-text text-transparent mb-2 sm:mb-3 md:mb-4 break-words md:text-left">
                {user?.name}
              </h1>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-2 sm:gap-4 md:gap-6 text-gray-600 mb-3 sm:mb-4 md:mb-6 w-full">
                <div className="flex items-center space-x-2 bg-white/70 px-3 py-2 md:px-5 md:py-3 rounded-lg shadow-sm w-full justify-center md:justify-start">
                  <Briefcase className="h-5 w-5 md:h-6 md:w-6 text-[#2c6f4a]" />
                  <span className="font-medium text-sm md:text-base">{user?.interest}</span>
                </div>
                <div className="flex items-center space-x-2 bg-white/70 px-3 py-2 md:px-5 md:py-3 rounded-lg shadow-sm w-full justify-center md:justify-start">
                  <MapPin className="h-5 w-5 md:h-6 md:w-6 text-[#2c6f4a]" />
                  <span className="font-medium text-sm md:text-base">{user?.city}</span>
                </div>
                <div className="flex items-center space-x-2 bg-white/70 px-3 py-2 md:px-5 md:py-3 rounded-lg shadow-sm w-full justify-center md:justify-start">
                  <Calendar className="h-5 w-5 md:h-6 md:w-6 text-[#2c6f4a]" />
                  <span className="font-medium text-sm md:text-base">Age: {user?.age}</span>
                </div>
              </div>
              <p className="text-gray-600 text-base sm:text-lg md:text-xl bg-white/50 px-3 py-2 md:px-5 md:py-3 rounded-lg inline-block break-all w-full text-center md:text-left">
                {user?.email}
              </p>
            </div>
          </div>

          {/* Enhanced Stats */}
          <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-4 gap-2 sm:gap-6 md:gap-10 mt-6 sm:mt-8 md:mt-10 pt-6 sm:pt-8 md:pt-10 border-t border-green-200/50 w-full">
            <div className="text-center bg-gradient-to-br from-white to-green-50 p-3 sm:p-4 md:p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 w-full">
              <div className="text-2xl sm:text-3xl md:text-4xl font-bold bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] bg-clip-text text-transparent">
                {userStats.totalPosts}
              </div>
              <div className="text-xs sm:text-sm md:text-base text-gray-600 font-medium mt-1">
                Posts
              </div>
              <FileText className="h-5 w-5 sm:h-6 sm:w-6 md:h-8 md:w-8 text-[#2c6f4a] mx-auto mt-2" />
            </div>
            <div className="text-center bg-gradient-to-br from-white to-blue-50 p-3 sm:p-4 md:p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 w-full">
              <div className="text-2xl sm:text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent">
                {userStats.totalVotes}
              </div>
              <div className="text-xs sm:text-sm md:text-base text-gray-600 font-medium mt-1">
                Votes
              </div>
              <ThumbsUp className="h-5 w-5 sm:h-6 sm:w-6 md:h-8 md:w-8 text-blue-600 mx-auto mt-2" />
            </div>
            <div className="text-center bg-gradient-to-br from-white to-purple-50 p-3 sm:p-4 md:p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 w-full">
              <div className="text-2xl sm:text-3xl md:text-4xl font-bold bg-gradient-to-r from-purple-600 to-purple-700 bg-clip-text text-transparent">
                {userStats.totalComments}
              </div>
              <div className="text-xs sm:text-sm md:text-base text-gray-600 font-medium mt-1">
                Comments
              </div>
              <MessageCircle className="h-5 w-5 sm:h-6 sm:w-6 md:h-8 md:w-8 text-purple-600 mx-auto mt-2" />
            </div>
            <div className="text-center bg-gradient-to-br from-white to-yellow-50 p-3 sm:p-4 md:p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 w-full">
              <div className="text-2xl sm:text-3xl md:text-4xl font-bold bg-gradient-to-r from-[#ffc35a] to-[#ffb84d] bg-clip-text text-transparent">
                {userStats.totalBookmarks}
              </div>
              <div className="text-xs sm:text-sm md:text-base text-gray-600 font-medium mt-1">
                Bookmarks
              </div>
              <Bookmark className="h-5 w-5 sm:h-6 sm:w-6 md:h-8 md:w-8 text-[#ffc35a] mx-auto mt-2" />
            </div>
          </div>
        </div>

        {/* Enhanced Tabs */}
        <div className="bg-gradient-to-br from-white to-green-50/30 rounded-2xl shadow-xl border border-green-200/50 overflow-hidden w-full">
          <div className="border-b border-green-200/30 bg-gradient-to-r from-green-50/50 to-white overflow-x-auto">
            <nav className="flex space-x-1 px-2 sm:px-6 min-w-max w-full">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 sm:space-x-3 py-3 sm:py-4 md:py-5 px-3 sm:px-6 md:px-8 border-b-2 font-semibold text-xs sm:text-sm md:text-base transition-all duration-300 relative whitespace-nowrap w-full sm:w-auto justify-center sm:justify-start ${
                      activeTab === tab.id
                        ? "border-[#2c6f4a] text-[#2c6f4a] bg-gradient-to-t from-green-50 to-transparent"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gradient-to-t hover:from-gray-50 hover:to-transparent"
                    }`}
                  >
                    <Icon
                      className={`h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 ${
                        activeTab === tab.id ? "text-[#2c6f4a]" : ""
                      }`}
                    />
                    <span>{tab.label}</span>
                    {activeTab === tab.id && (
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c]"></div>
                    )}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Enhanced Tab Content */}
          <div className="p-2 sm:p-8 md:p-12 w-full">
            {activeTab === "overview" && (
              <div className="space-y-8">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] rounded-lg flex items-center justify-center">
                    <User className="h-5 w-5 text-white" />
                  </div>
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] bg-clip-text text-transparent">
                    {t("profile.overview")}
                  </h2>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="bg-gradient-to-br from-green-50 to-white p-6 rounded-xl shadow-lg border border-green-200/50 w-full md:text-left md:items-start">
                    <h3 className="font-bold text-gray-900 mb-4 text-lg flex items-center space-x-2">
                      <div className="w-6 h-6 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">ℹ</span>
                      </div>
                      <span>Personal Information</span>
                    </h3>
                    <div className="space-y-3 text-sm text-gray-600">
                      <div className="flex justify-between py-2 border-b border-green-100">
                        <strong className="text-gray-800">Name:</strong>
                        <span>{user?.name}</span>
                      </div>
                      <div className="flex justify-between py-2 border-b border-green-100">
                        <strong className="text-gray-800">Email:</strong>
                        <span>{user?.email}</span>
                      </div>
                      <div className="flex justify-between py-2 border-b border-green-100">
                        <strong className="text-gray-800">Phone:</strong>
                        <span>{user?.phoneNumber}</span>
                      </div>
                      <div className="flex justify-between py-2 border-b border-green-100">
                        <strong className="text-gray-800">Age:</strong>
                        <span>{user?.age}</span>
                      </div>
                      <div className="flex justify-between py-2 border-b border-green-100">
                        <strong className="text-gray-800">City:</strong>
                        <span>{user?.city}</span>
                      </div>
                      <div className="flex justify-between py-2">
                        <strong className="text-gray-800">Interest:</strong>
                        <span>{user?.interest}</span>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gradient-to-br from-blue-50 to-white p-6 rounded-xl shadow-lg border border-blue-200/50 w-full md:text-left md:items-start">
                    <h3 className="font-bold text-gray-900 mb-4 text-lg flex items-center space-x-2">
                      <div className="w-6 h-6 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">📊</span>
                      </div>
                      <span>Activity Summary</span>
                    </h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm">
                        <span className="text-gray-700">Total Posts</span>
                        <span className="font-bold text-[#2c6f4a] text-lg">
                          {userStats.totalPosts}
                        </span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm">
                        <span className="text-gray-700">Total Votes</span>
                        <span className="font-bold text-blue-600 text-lg">
                          {userStats.totalVotes}
                        </span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm">
                        <span className="text-gray-700">Total Comments</span>
                        <span className="font-bold text-purple-600 text-lg">
                          {userStats.totalComments}
                        </span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm">
                        <span className="text-gray-700">Bookmarks</span>
                        <span className="font-bold text-[#ffc35a] text-lg">
                          {userStats.totalBookmarks}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "posts" && (
              <div className="space-y-6">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] rounded-lg flex items-center justify-center">
                    <FileText className="h-5 w-5 text-white" />
                  </div>
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] bg-clip-text text-transparent">
                    {t("profile.myPosts")}
                  </h2>
                </div>
                {userPosts.length > 0 ? (
                  <div className="space-y-4">
                    {(userPosts ?? []).map((post) => {
                      const translatedPost = translatePost(post);
                      return (
                        <div
                          key={post.id}
                          className="bg-gradient-to-br from-white to-green-50/30 border border-green-200/50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 w-full"
                        >
                          <div className="flex justify-between items-start mb-3">
                            <h3 className="font-bold text-gray-900 text-lg flex-1">
                              {translatedPost.question}
                            </h3>
                            <button
                              onClick={() => handleDeletePost(post.id)}
                              disabled={isDeleting === post.id}
                              className={`ml-4 p-2 rounded-lg transition-colors ${
                                isDeleting === post.id
                                  ? "text-gray-400 cursor-not-allowed"
                                  : "text-red-500 hover:text-red-700 hover:bg-red-50"
                              }`}
                              title="Delete post"
                            >
                              {isDeleting === post.id ? (
                                <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-red-500"></div>
                              ) : (
                                <Trash2 className="h-4 w-4" />
                              )}
                            </button>
                          </div>
                          <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                            {translatedPost.content}
                          </p>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium">
                                {new Date(post.createdAt).toLocaleDateString()}
                              </span>
                            </div>
                            <div className="flex items-center space-x-4 text-xs">
                              <span className="flex items-center space-x-1 bg-green-100 text-green-700 px-2 py-1 rounded-full">
                                <span>👍</span>
                                <span className="font-medium">
                                  {post.upvotes}
                                </span>
                              </span>
                              <span className="flex items-center space-x-1 bg-red-100 text-red-700 px-2 py-1 rounded-full">
                                <span>👎</span>
                                <span className="font-medium">
                                  {post.downvotes}
                                </span>
                              </span>
                              <span className="flex items-center space-x-1 bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                                <span>💬</span>
                                <span className="font-medium">
                                  {post.comments}
                                </span>
                              </span>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-12 w-full">
                    <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {t("profile.noPosts")}
                    </h3>
                    <p className="text-gray-500">{t("profile.startSharing")}</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === "votes" && (
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-gray-900">
                  {t("profile.myVotes")}
                </h2>
                {(userVotes ?? []).length > 0 ? (
                  (userVotes ?? []).map((vote) => (
                    <div
                      key={vote.id}
                      className="border border-gray-200 rounded-lg p-4 w-full"
                    >
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium text-gray-900">
                          {vote.question.title}
                        </h3>
                        <span
                          className={`px-2 py-1 rounded text-xs font-medium ${
                            vote.type === "UPVOTE"
                              ? "bg-green-100 text-green-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {vote.type === "UPVOTE"
                            ? `👍 ${t("profile.upvote")}`
                            : `👎 ${t("profile.downvote")}`}
                        </span>
                      </div>
                      <p className="text-gray-500 text-sm mt-2">
                        {t("profile.votedOn")}{" "}
                        {new Date(vote.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500">{t("profile.noVotes")}</p>
                )}
              </div>
            )}

            {activeTab === "comments" && (
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-gray-900">
                  {t("profile.myComments")}
                </h2>
                {(userComments ?? []).length > 0 ? (
                  (userComments ?? []).map((comment) => (
                    <div
                      key={comment.id}
                      className="bg-gradient-to-br from-white to-green-50/30 border border-green-200/50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 w-full"
                    >
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-bold text-gray-900 text-lg flex-1">
                          {comment.question.title}
                        </h3>
                        <span className="px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                          {t("profile.comment")}
                        </span>
                      </div>
                      <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                        {comment.content}
                      </p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium">
                            {new Date(comment.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex items-center space-x-4 text-xs">
                          <span className="flex items-center space-x-1 bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                            <span>💬</span>
                            <span className="font-medium">
                              {comment.isReply ? "Reply" : "Comment"}
                            </span>
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-12 w-full">
                    <MessageCircle className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {t("profile.noComments")}
                    </h3>
                    
                  </div>
                )}
              </div>
            )}

            {activeTab === "bookmarks" && (
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-gray-900">
                  {t("profile.myBookmarks")}
                </h2>
                {(bookmarkedPosts ?? []).length > 0 ? (
                  (bookmarkedPosts ?? []).map((post) => {
                    const translatedPost = translatePost(post);
                    return (
                      <div
                        key={post.id}
                        className="bg-gradient-to-br from-white to-green-50/30 border border-green-200/50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 w-full"
                      >
                        <div className="flex justify-between items-start mb-3">
                          <h3 className="font-bold text-gray-900 text-lg flex-1">
                            {translatedPost.question}
                          </h3>
                          <button
                            onClick={() => handleDeletePost(post.id)}
                            disabled={isDeleting === post.id}
                            className={`ml-4 p-2 rounded-lg transition-colors ${
                              isDeleting === post.id
                                ? "text-gray-400 cursor-not-allowed"
                                : "text-red-500 hover:text-red-700 hover:bg-red-50"
                            }`}
                            title="Delete post"
                          >
                            {isDeleting === post.id ? (
                              <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-red-500"></div>
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </button>
                        </div>
                        <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                          {translatedPost.content}
                        </p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4 text-xs text-gray-500">
                            <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium">
                              {new Date(post.createdAt).toLocaleDateString()}
                            </span>
                          </div>
                          <div className="flex items-center space-x-4 text-xs">
                            <span className="flex items-center space-x-1 bg-green-100 text-green-700 px-2 py-1 rounded-full">
                              <span>👍</span>
                              <span className="font-medium">
                                {post.upvotes}
                              </span>
                            </span>
                            <span className="flex items-center space-x-1 bg-red-100 text-red-700 px-2 py-1 rounded-full">
                              <span>👎</span>
                              <span className="font-medium">
                                {post.downvotes}
                              </span>
                            </span>
                            <span className="flex items-center space-x-1 bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                              <span>💬</span>
                              <span className="font-medium">
                                {Array.isArray(post.comments) ? post.comments.length : post.comments}
                              </span>
                            </span>
                          </div>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="text-center py-12 w-full">
                    <Bookmark className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {t("profile.noBookmarks")}
                    </h3>
                   
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

