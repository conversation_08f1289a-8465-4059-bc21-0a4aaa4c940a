"use client";

import React, { useEffect, useState } from "react";
import { Sparkles } from "lucide-react";

interface ComingSoonModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ComingSoonModal: React.FC<ComingSoonModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => setShow(true), 50); // smooth enter
    } else {
      setShow(false);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30 backdrop-blur-sm">
      <div
        className={`bg-white rounded-xl shadow-xl w-[320px] p-6 text-center transform transition-all duration-300 ${
          show ? "scale-100 opacity-100" : "scale-95 opacity-0"
        }`}
      >
        <div className="flex justify-center items-center mb-3 text-blue-500">
          <Sparkles className="w-6 h-6" />
        </div>
        <h2 className="text-lg font-semibold text-gray-800 mb-2">
          Feature Coming Soon
        </h2>
        <p className="text-sm text-gray-600 mb-5">
          We’re working on it — exciting things ahead!
        </p>
        <button
          onClick={onClose}
          className="bg-blue-100 text-blue-700 text-sm px-4 py-1.5 rounded-md hover:bg-blue-200 transition"
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default ComingSoonModal;
