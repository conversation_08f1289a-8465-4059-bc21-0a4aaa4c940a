import { apiService } from "./apiService";

export interface FeedbackData {
  rating: number; // 1-5
  improvement?: string;
  featureRequest?: string;
}

export interface AdBookingData {
  name: string;
  phone: string;
  email: string;
  city: string;
  businessIndustry: string; // Frontend field name
}

export interface InvestmentData {
  name: string;
  phone: string;
  email: string;
  investmentReason: string;
  believesPlatformWillGrow: boolean;
}

export const feedbackService = {
  async submitFeedback(data: FeedbackData) {
    console.log("=== FEEDBACK SUBMISSION DEBUG ===");
    console.log("1. Original data:", data);
    console.log("2. User token exists:", !!localStorage.getItem("token"));
    console.log(
      "3. User data:",
      JSON.parse(localStorage.getItem("user") || "{}")
    );

    try {
      const response = await apiService.request("/api/feedback", {
        method: "POST",
        body: JSON.stringify(data),
      });

      console.log("4. Raw API response:", response);
      console.log("5. Response success:", response.success);
      console.log("6. Response error:", response.error);
      console.log("=== END DEBUG ===");

      if (!response.success) {
        console.error("Feedback submission failed:", response.error);
        return {
          success: false,
          error: response.error || "Failed to submit feedback",
        };
      }

      return response;
    } catch (error) {
      console.error("7. Catch block error:", error);
      return {
        success: false,
        error: "Failed to submit feedback - network error",
      };
    }
  },

  async submitAdBooking(data: AdBookingData) {
    console.log("=== AD BOOKING SUBMISSION DEBUG ===");
    console.log("1. Original frontend data:", data);
    console.log("2. User token exists:", !!localStorage.getItem("token"));
    console.log(
      "3. User data:",
      JSON.parse(localStorage.getItem("user") || "{}")
    );

    try {
      // Map frontend field to backend field
      const backendData = {
        name: data.name,
        phone: data.phone,
        email: data.email,
        city: data.city,
        industry: data.businessIndustry, // Backend expects "industry"
      };

      console.log("4. Mapped backend data:", backendData);
      console.log("5. Making API call to /api/ad-booking");

      const response = await apiService.request("/api/ad-booking", {
        method: "POST",
        body: JSON.stringify(backendData),
      });

      console.log("6. Raw API response:", response);
      console.log("7. Response success:", response.success);
      console.log("8. Response data:", response.data);
      console.log("9. Response error:", response.error);
      console.log("=== END AD BOOKING DEBUG ===");

      if (!response.success) {
        console.error("Ad booking submission failed:", response.error);
        return {
          success: false,
          error: response.error || "Failed to submit ad booking",
        };
      }

      return response;
    } catch (error) {
      console.error("10. Catch block error:", error);
      return {
        success: false,
        error: "Failed to submit ad booking - network error",
      };
    }
  },

  async submitInvestment(data: InvestmentData) {
    console.log("=== INVESTMENT SUBMISSION DEBUG ===");
    console.log("1. Original data:", data);
    console.log("2. Data keys:", Object.keys(data));
    console.log("3. Data values:", Object.values(data));
    console.log("4. User token exists:", !!localStorage.getItem("token"));

    // Check each field individually
    console.log("5. Field by field check:");
    console.log("   - name:", `"${data.name}" (length: ${data.name?.length})`);
    console.log(
      "   - phone:",
      `"${data.phone}" (length: ${data.phone?.length})`
    );
    console.log(
      "   - email:",
      `"${data.email}" (length: ${data.email?.length})`
    );
    console.log(
      "   - investmentReason:",
      `"${data.investmentReason}" (length: ${data.investmentReason?.length})`
    );
    console.log(
      "   - believesPlatformWillGrow:",
      data.believesPlatformWillGrow
    );

    // Validate all required fields
    const requiredFields = [
      "name",
      "phone",
      "email",
      "investmentReason",
      "believesPlatformWillGrow",
    ];
    const missingFields = requiredFields.filter((field) => {
      const value = data[field as keyof InvestmentData];
      let isEmpty = false;
      if (typeof value === "string") {
        isEmpty = !value || value.trim() === "";
      } else if (typeof value === "boolean") {
        isEmpty = value === undefined;
      } else {
        isEmpty = value === undefined || value === null;
      }
      console.log(`   - ${field}: ${isEmpty ? "MISSING/EMPTY" : "OK"}`);
      return isEmpty;
    });

    if (missingFields.length > 0) {
      console.log("6. Missing fields:", missingFields);
      return {
        success: false,
        error: `Missing required fields: ${missingFields.join(", ")}`,
      };
    }

    console.log("6. All required fields present");
    console.log("7. JSON.stringify result:", JSON.stringify(data));
    console.log("8. Making API call to /api/investment/submit");

    try {
      const response = await apiService.request("/api/investment/submit", {
        method: "POST",
        body: JSON.stringify(data),
      });

      console.log("9. Raw API response:", response);
      console.log("10. Response success:", response.success);
      console.log("11. Response error:", response.error);
      console.log("12. Response data:", response.data);
      console.log("=== END INVESTMENT DEBUG ===");

      if (!response.success) {
        console.error("Investment submission failed:", response.error);
        return {
          success: false,
          error: response.error || "Failed to submit investment",
        };
      }

      return response;
    } catch (error) {
      console.error("13. Catch block error:", error);
      return {
        success: false,
        error: "Failed to submit investment - network error",
      };
    }
  },
};
