"use client";

import Link from "next/link";

export default function PaymentFailure() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-rose-50 flex items-center justify-center px-4">
      <div className="bg-white rounded-2xl shadow-xl border border-red-200 p-8 max-w-md w-full text-center">
        <div className="mb-6">
          <div className="w-20 h-20 bg-gradient-to-r from-red-500 to-rose-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
            <svg
              className="w-10 h-10 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={3}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </div>
        </div>

        <h2 className="text-3xl font-bold text-red-700 mb-3">Payment Failed</h2>

        <p className="text-slate-600 mb-6 leading-relaxed">
          We couldn't process your payment. This could be due to insufficient
          funds, network issues, or bank restrictions.
        </p>

        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <h3 className="text-sm font-semibold text-red-700 mb-2">
            Common reasons:
          </h3>
          <ul className="text-xs text-red-600 text-left space-y-1">
            <li>• Insufficient account balance</li>
            <li>• Network connectivity issues</li>
            <li>• Bank server temporarily down</li>
            <li>• Transaction limit exceeded</li>
          </ul>
        </div>

        <div className="space-y-3">
          <Link
            href="/"
            className="block w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-4 px-6 rounded-lg font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            Try Again
          </Link>

          <Link
            href="/support"
            className="block w-full border-2 border-slate-300 text-slate-700 py-3 px-6 rounded-lg font-medium hover:border-slate-400 hover:bg-slate-50 transition-all duration-200"
          >
            Contact Support
          </Link>

          <p className="text-xs text-slate-500 mt-4">
            💬 Need help? Our support team is available 24/7
          </p>
        </div>
      </div>
    </div>
  );
}
