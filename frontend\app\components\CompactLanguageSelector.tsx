'use client';

import { useState, useRef, useEffect } from 'react';
import { Globe, ChevronDown } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

export default function CompactLanguageSelector() {
  const { currentLanguage, languages, changeLanguage, t } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleLanguageChange = (language: any) => {
    changeLanguage(language);
    setIsOpen(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 text-sm bg-gradient-to-r from-white to-blue-50/80 border border-blue-200/50 rounded-full hover:from-blue-50 hover:to-blue-100/80 hover:border-[#2c64fa]/30 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#2c64fa]/20 focus:border-[#2c64fa] shadow-sm hover:shadow-md backdrop-blur-sm"
      >
        <div className="flex items-center space-x-1.5">
          <Globe className="h-4 w-4 bg-gradient-to-r from-[#2c64fa] to-[#4a7cfc] bg-clip-text text-transparent" />
          <span className="text-lg">{currentLanguage.flag}</span>
          <span className="text-gray-700 font-medium text-xs hidden sm:block">
            {currentLanguage.code.toUpperCase()}
          </span>
        </div>
        <ChevronDown className={`h-3 w-3 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-xl z-50 max-h-80 overflow-y-auto">
          <div className="p-2">
            <div className="text-xs font-medium text-gray-500 mb-2 px-2 uppercase tracking-wide">
              {t('common.language')}
            </div>
            <div className="space-y-1">
              {languages.map((language) => (
                <button
                  key={language.code}
                  onClick={() => handleLanguageChange(language)}
                  className={`w-full flex items-center space-x-3 px-3 py-2 text-sm rounded-md hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100 hover:text-[#2c64fa] transition-all duration-200 ${
                    currentLanguage.code === language.code
                      ? 'bg-gradient-to-r from-[#2c64fa] to-[#4a7cfc] text-white'
                      : 'text-gray-700'
                  }`}
                >
                  <span className="text-base">{language.flag}</span>
                  <div className="flex flex-col items-start flex-1">
                    <span className="font-medium text-sm">{language.nativeName}</span>
                    <span className={`text-xs ${currentLanguage.code === language.code ? 'text-blue-100' : 'text-gray-500'}`}>
                      {language.name}
                    </span>
                  </div>
                  {currentLanguage.code === language.code && (
                    <div className="w-2 h-2 bg-white rounded-full" />
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
