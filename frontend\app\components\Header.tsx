"use client";

import {
  Bell,
  User,
  Home,
  GraduationCap,
  Globe,
  Bookmark,
  LogIn,
  UserPlus,
  ChevronDown,
} from "lucide-react";
import Image from "next/image";
import { useState, useEffect } from "react";
import { useLanguage } from "../contexts/LanguageContext";
import { useAuth } from "../contexts/AuthContext";
import CompactLanguageSelector from "./CompactLanguageSelector";
import UserProfile from "./UserProfile";
import AuthModal from "./AuthModal";
import BookmarkPanel from "./BookmarkPanel";
import NotificationPanel from "./NotificationPanel";
import { Post } from "../services/postService";
import { useNotifications } from "../hooks/useNotifications";
import Link from "next/link";
import SearchSection from "./SearchSection";
import AdvertisementModal from "./AdvertisementModal";
import InvestmentTile from "./InvestmentTile";
import FeedbackTile from "./FeedbackTile";
import { usePathname } from "next/navigation";
import NetworkDemographics from "./NetworkDemographics";

export default function Header({ searchTerm, setSearchTerm }: { searchTerm: string; setSearchTerm: (val: string) => void }) {
  const { t } = useLanguage();
  const { isAuthenticated, isLoading } = useAuth();
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [bookmarkPanelOpen, setBookmarkPanelOpen] = useState(false);
  const [notificationPanelOpen, setNotificationPanelOpen] = useState(false);
  const [bookmarkCount, setBookmarkCount] = useState(0);
  const [mobileNavOpen, setMobileNavOpen] = useState(false);
  const [adModalOpen, setAdModalOpen] = useState(false);
  const [supportModalOpen, setSupportModalOpen] = useState(false);
  const [feedbackModalOpen, setFeedbackModalOpen] = useState(false);
  const [showDemographicsModal, setShowDemographicsModal] = useState(false);

  // Add notification hook
  const { unreadCount, fetchNotifications } = useNotifications();

  // Fetch notifications on component mount to get initial unread count
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  const handleNotificationClick = () => {
    setNotificationPanelOpen(!notificationPanelOpen);
    if (bookmarkPanelOpen) {
      setBookmarkPanelOpen(false);
    }
  };

  const handleBookmarkClick = () => {
    setBookmarkPanelOpen(!bookmarkPanelOpen);
    // Close notification panel if open
    if (notificationPanelOpen) {
      setNotificationPanelOpen(false);
    }
  };

  const handleBookmarkCountChange = (count: number) => {
    setBookmarkCount(count);
  };

  const handlePostClick = (post: Post) => {
    // This would navigate to the specific post
    console.log("Navigate to post:", post.id);
  };

  const handleNotificationPostClick = (postId: string) => {
    // This would navigate to the specific post
    console.log("Navigate to post from notification:", postId);
  };

  // Helper to close all modals
  const closeAllModals = () => {
    setAdModalOpen(false);
    setSupportModalOpen(false);
    setFeedbackModalOpen(false);
    setShowDemographicsModal(false);
  };

  const pathname = usePathname();

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-50 shadow-sm">
      <div className="max-w-7xl mx-auto px-4">
        {/* HEADER ROW */}
        <div className="flex items-center justify-between h-16">
          {/* Logo and hamburger menu */}
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Image
                src="/images/Buyers Network Final.png"
                alt="Buyers Network by PropertyLens.in"
                width={1000}
                height={140}
                className="h-8 w-auto max-w-[120px] sm:h-10 sm:max-w-sm"
                priority
              />
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="flex md:hidden">
            <button
              onClick={() => setMobileNavOpen((open) => !open)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-[#2c6f4a] hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[#2c6f4a]"
              aria-label="Open main menu"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>

          {/* Spacer */}
          <div className="flex-1 hidden md:block"></div>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-4">
            {/* Language Selector */}
            <CompactLanguageSelector />

            <Link
              href="/"
              className="flex items-center space-x-1 text-gray-700 hover:text-[#2c6f4a] hover:bg-gradient-to-r hover:from-[#ffc35a]/20 hover:to-[#ffc35a]/30 px-3 py-2 rounded-full transition-all duration-200 group"
            >
              <Home className="h-5 w-5 group-hover:text-[#ffc35a]" />
              <span className="hidden md:block font-medium group-hover:text-[#2c6f4a]">
                {t("navigation.home")}
              </span>
            </Link>
            <Link
              href="/network"
              className="flex items-center space-x-1 text-gray-700 hover:text-[#2c6f4a] hover:bg-gradient-to-r hover:from-[#ffc35a]/20 hover:to-[#ffc35a]/30 px-3 py-2 rounded-full transition-all duration-200 group"
            >
              <GraduationCap className="h-5 w-5 group-hover:text-[#ffc35a]" />
              <span className="hidden md:block font-medium group-hover:text-[#2c6f4a]">
                {t("navigation.school")}
              </span>
            </Link>
            <a
              href="https://www.propertylens.in"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-1 text-gray-700 hover:text-[#2c6f4a] hover:bg-gradient-to-r hover:from-[#ffc35a]/20 hover:to-[#ffc35a]/30 px-3 py-2 rounded-full transition-all duration-200 group"
            >
              <Globe className="h-5 w-5 group-hover:text-[#ffc35a]" />
              <span className="hidden md:block font-medium group-hover:text-[#2c6f4a]">
                {t("header.propertyLensIn")}
              </span>
            </a>

            {/* Notifications */}
            <div className="relative">
              <button
                onClick={handleNotificationClick}
                className="relative p-2 text-gray-700 hover:text-[#2c6f4a] hover:bg-gradient-to-r hover:from-[#ffc35a]/20 hover:to-[#ffc35a]/30 rounded-full transition-all duration-200 group"
                aria-label={`Notifications ${
                  unreadCount > 0 ? `(${unreadCount} unread)` : ""
                }`}
              >
                <Bell className="h-5 w-5 group-hover:text-[#ffc35a]" />
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium shadow-sm animate-pulse">
                    {unreadCount > 9 ? "9+" : unreadCount}
                  </span>
                )}
              </button>

              {/* Notification Panel */}
              <NotificationPanel
                isOpen={notificationPanelOpen}
                onClose={() => setNotificationPanelOpen(false)}
              />
            </div>

            {/* Bookmarks */}
            <div className="relative">
              <button
                onClick={handleBookmarkClick}
                className="relative p-2 text-gray-700 hover:text-[#2c6f4a] hover:bg-gradient-to-r hover:from-[#ffc35a]/20 hover:to-[#ffc35a]/30 rounded-full transition-all duration-200 group"
              >
                <Bookmark className="h-5 w-5 group-hover:text-[#ffc35a]" />
                {bookmarkCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium shadow-sm">
                    {bookmarkCount}
                  </span>
                )}
              </button>

              {/* Bookmark Panel */}
              <BookmarkPanel
                isOpen={bookmarkPanelOpen}
                onClose={() => setBookmarkPanelOpen(false)}
                onPostClick={handlePostClick}
                onBookmarkCountChange={handleBookmarkCountChange}
                onPostUpdate={() => {}}
              />
            </div>

            {/* Authentication Section */}
            <div className="flex items-center space-x-2">
              {isLoading ? (
                <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
              ) : isAuthenticated ? (
                <UserProfile />
              ) : (
                <button
                  onClick={() => setAuthModalOpen(true)}
                  className="flex items-center space-x-2 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] text-white hover:from-[#3a8b5c] hover:to-[#2c6f4a] px-4 py-2 rounded-full transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  <UserPlus className="h-5 w-5" />
                  <span className="font-medium">Login/Signup</span>
                </button>
              )}
            </div>
          </nav>
        </div>
        {/* Mobile nav panel at the top, just below header row */}
        {/* Mobile Nav Panel Overlay */}
        {mobileNavOpen && (
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 z-40 bg-black bg-opacity-40 transition-opacity duration-200"
              onClick={() => setMobileNavOpen(false)}
              aria-label="Close mobile navigation"
            />
            {/* Slide-in Panel */}
            <nav
              className="fixed top-0 right-0 h-full w-4/5 max-w-xs bg-white shadow-2xl z-50 flex flex-col space-y-2 p-4 rounded-l-2xl animate-in fade-in slide-in-from-right duration-200 overflow-y-auto"
              style={{ minWidth: '260px' }}
            >
              {/* UserProfile at the top for mobile nav */}
              <div className="flex items-center space-x-3 mb-4">
                {isLoading ? (
                  <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse" />
                ) : isAuthenticated ? (
                  <UserProfile showNameOnMobile={true} />
                ) : (
                  <button
                    onClick={() => {
                      setAuthModalOpen(true);
                      setMobileNavOpen(false);
                    }}
                    className="flex items-center space-x-2 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] text-white hover:from-[#3a8b5c] hover:to-[#2c6f4a] px-4 py-2 rounded-full transition-all duration-200 shadow-lg hover:shadow-xl w-full justify-center"
                  >
                    <UserPlus className="h-5 w-5" />
                    <span className="font-medium">Login/Signup</span>
                  </button>
                )}
              </div>
              <button
                className="self-end text-gray-400 hover:text-gray-600 text-2xl mb-2"
                onClick={() => setMobileNavOpen(false)}
                aria-label="Close"
              >
                ×
              </button>
              <CompactLanguageSelector />
              <Link
                href="/"
                onClick={() => setMobileNavOpen(false)}
                className="flex items-center space-x-2 text-gray-700 hover:text-[#2c6f4a] hover:bg-gradient-to-r hover:from-[#ffc35a]/20 hover:to-[#ffc35a]/30 px-4 py-3 rounded-full transition-all duration-200 group"
              >
                <Home className="h-5 w-5 group-hover:text-[#ffc35a]" />
                <span className="font-medium group-hover:text-[#2c6f4a]">
                  {t("navigation.home")}
                </span>
              </Link>
              <Link
                href="/network"
                onClick={() => setMobileNavOpen(false)}
                className="flex items-center space-x-2 text-gray-700 hover:text-[#2c6f4a] hover:bg-gradient-to-r hover:from-[#ffc35a]/20 hover:to-[#ffc35a]/30 px-4 py-3 rounded-full transition-all duration-200 group"
              >
                <GraduationCap className="h-5 w-5 group-hover:text-[#ffc35a]" />
                <span className="font-medium group-hover:text-[#2c6f4a]">
                  {t("navigation.school")}
                </span>
              </Link>
              <a
                href="https://www.propertylens.in"
                target="_blank"
                rel="noopener noreferrer"
                onClick={() => setMobileNavOpen(false)}
                className="flex items-center space-x-2 text-gray-700 hover:text-[#2c6f4a] hover:bg-gradient-to-r hover:from-[#ffc35a]/20 hover:to-[#ffc35a]/30 px-4 py-3 rounded-full transition-all duration-200 group"
              >
                <Globe className="h-5 w-5 group-hover:text-[#ffc35a]" />
                <span className="font-medium group-hover:text-[#2c6f4a]">
                  {t("header.propertyLensIn")}
                </span>
              </a>
              <div className="relative flex items-center w-full">
                <button
                  onClick={handleNotificationClick}
                  className="relative flex items-center w-full px-4 py-3 rounded-full text-gray-700 hover:text-[#2c6f4a] hover:bg-gradient-to-r hover:from-[#ffc35a]/20 hover:to-[#ffc35a]/30 transition-all duration-200 group justify-start"
                  aria-label={`Notifications ${unreadCount > 0 ? `(${unreadCount} unread)` : ""}`}
                >
                  <Bell className="h-5 w-5 group-hover:text-[#ffc35a]" />
                  <span className="ml-2 font-medium inline md:hidden">Notifications</span>
                  {unreadCount > 0 && (
                    <span className="absolute right-4 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium shadow-sm animate-pulse">
                      {unreadCount > 9 ? "9+" : unreadCount}
                    </span>
                  )}
                </button>
                <NotificationPanel
                  isOpen={notificationPanelOpen}
                  onClose={() => setNotificationPanelOpen(false)}
                />
              </div>
              <div className="relative flex items-center w-full">
                <button
                  onClick={handleBookmarkClick}
                  className="relative flex items-center w-full px-4 py-3 rounded-full text-gray-700 hover:text-[#2c6f4a] hover:bg-gradient-to-r hover:from-[#ffc35a]/20 hover:to-[#ffc35a]/30 transition-all duration-200 group justify-start"
                >
                  <Bookmark className="h-5 w-5 group-hover:text-[#ffc35a]" />
                  <span className="ml-2 font-medium inline md:hidden">Bookmarks</span>
                  {bookmarkCount > 0 && (
                    <span className="absolute right-4 bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium shadow-sm">
                      {bookmarkCount}
                    </span>
                  )}
                </button>
                <BookmarkPanel
                  isOpen={bookmarkPanelOpen}
                  onClose={() => setBookmarkPanelOpen(false)}
                  onPostClick={handlePostClick}
                  onBookmarkCountChange={handleBookmarkCountChange}
                  onPostUpdate={() => {}}
                />
              </div>
            </nav>
          </>
        )}
        {/* Mobile Search Bar and quick actions below nav panel - only show on home page */}
        {pathname === "/" && (
          <div className="block md:hidden pt-2 pb-2">
            <SearchSection searchTerm={searchTerm} setSearchTerm={setSearchTerm} />
            <div className="flex flex-row space-x-2 mt-2 justify-between">
              <button
                className="flex-1 bg-[#ffc35a] text-yellow-900 font-semibold rounded-lg py-2 px-2 text-xs"
                onClick={() => {
                  closeAllModals();
                  setAdModalOpen(true);
                }}
              >
                Advertisement
              </button>
              <button
                className="flex-1 bg-[#ffc35a] text-yellow-900 font-semibold rounded-lg py-2 px-2 text-xs"
                onClick={() => {
                  closeAllModals();
                  setSupportModalOpen(true);
                }}
              >
                Support Us
              </button>
              <button
                className="flex-1 bg-[#ffc35a] text-yellow-900 font-semibold rounded-lg py-2 px-2 text-xs"
                onClick={() => {
                  closeAllModals();
                  setFeedbackModalOpen(true);
                }}
              >
                Feedback
              </button>
              <button
                className="flex-1 bg-[#ffc35a] text-yellow-900 font-semibold rounded-lg py-2 px-2 text-xs"
                onClick={() => {
                  closeAllModals();
                  setShowDemographicsModal(true);
                }}
              >
                Demographics
              </button>
            </div>
            {adModalOpen && (
              <div
                className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40"
                onClick={closeAllModals}
              >
                <div
                  className="bg-white rounded-lg shadow-xl p-4 max-w-xs w-full mx-2 relative"
                  onClick={e => e.stopPropagation()}
                >
                  <button
                    className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 text-2xl z-10"
                    onClick={closeAllModals}
                    aria-label="Close"
                  >
                    ×
                  </button>
                  <AdvertisementModal isOpen={true} onClose={closeAllModals} slotTitle="" />
                </div>
              </div>
            )}
            {supportModalOpen && (
              <div
                className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40"
                onClick={closeAllModals}
              >
                <div
                  className="bg-white rounded-lg shadow-xl p-4 max-w-sm w-full mx-2 relative"
                  onClick={e => e.stopPropagation()}
                >
                  <button
                    className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 text-2xl z-10"
                    onClick={closeAllModals}
                    aria-label="Close"
                  >
                    ×
                  </button>
                  <InvestmentTile />
                </div>
              </div>
            )}
            {feedbackModalOpen && (
              <div
                className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40"
                onClick={closeAllModals}
              >
                <div
                  className="bg-white rounded-lg shadow-xl p-4 max-w-sm w-full mx-2 relative"
                  onClick={e => e.stopPropagation()}
                >
                  <button
                    className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 text-2xl z-10"
                    onClick={closeAllModals}
                    aria-label="Close"
                  >
                    ×
                  </button>
                  <FeedbackTile />
                </div>
              </div>
            )}
            {showDemographicsModal && (
              <div
                className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40"
                onClick={closeAllModals}
              >
                <div
                  className="bg-white rounded-lg shadow-xl p-4 max-w-sm w-full mx-2 relative"
                  onClick={e => e.stopPropagation()}
                >
                  <button
                    className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 text-2xl z-10"
                    onClick={closeAllModals}
                    aria-label="Close"
                  >
                    ×
                  </button>
                  <NetworkDemographics />
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
      />
    </header>
  );
}
