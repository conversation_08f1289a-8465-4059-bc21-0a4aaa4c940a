const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "https://buyers-backend-606425615474.asia-south1.run.app";

class ApiService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem("token");
    return {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const token = localStorage.getItem("token");
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || "https://buyers-backend-606425615474.asia-south1.run.app";
    const url = `${baseUrl}${endpoint}`;

    console.log("🌐 API Request Details:");
    console.log("- Endpoint:", endpoint);
    console.log("- Full URL:", url);
    console.log("- Method:", options.method || "GET");
    console.log("- Has token:", !!token);
    console.log("- Request body:", options.body);

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          "Content-Type": "application/json",
          ...(token && { Authorization: `Bearer ${token}` }),
          ...options.headers,
        },
      });

      console.log("📡 Response status:", response.status);
      console.log("📡 Response ok:", response.ok);

      const data = await response.json();
      console.log("📡 Response data:", data);

      if (!response.ok) {
        return {
          success: false,
          error: data.error || data.msg || data.message || "Request failed",
        };
      }

      // For DELETE requests, if we get a 200 status with a message, consider it successful
      if (options.method === "DELETE" && response.ok) {
        return {
          success: true,
          data,
        };
      }

      return {
        success: true,
        data,
      };
    } catch (error) {
      console.error("🚨 API request error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Network error",
      };
    }
  }

  get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "GET" });
  }

  post<T>(endpoint: string, body?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: body ? JSON.stringify(body) : undefined,
    });
  }

  put<T>(endpoint: string, body?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "PUT",
      body: body ? JSON.stringify(body) : undefined,
    });
  }

  delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "DELETE" });
  }
}

export const apiService = new ApiService();

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}
