"use client";

import { useState } from "react";
import { Star, MessageSquare, ThumbsDown, Plus, Send, X } from "lucide-react";
import { useLanguage } from "../contexts/LanguageContext";
import { feedbackService } from "../services/feedbackService";

export default function FeedbackTile() {
  const { t } = useLanguage();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [formData, setFormData] = useState({
    dislikes: "",
    suggestions: "",
    opinion: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if user is logged in
    const token = localStorage.getItem("token");
    if (!token) {
      alert("Please log in to submit feedback");
      return;
    }

    if (rating === 0) {
      alert("Please provide a rating before submitting");
      return;
    }

    setIsSubmitting(true);

    try {
      const feedbackData = {
        rating,
        improvement: formData.dislikes || undefined,
        featureRequest: formData.suggestions || undefined,
      };

      const response = await feedbackService.submitFeedback(feedbackData);

      if (response.success) {
        setSubmitted(true);
        // Reset form after 3 seconds
        setTimeout(() => {
          setSubmitted(false);
          setIsModalOpen(false);
          setRating(0);
          setHoverRating(0);
          setFormData({
            dislikes: "",
            suggestions: "",
            opinion: "",
          });
        }, 3000);
      } else {
        alert("Error: " + (response.error || "Failed to submit feedback"));
      }
    } catch (error) {
      alert("Network error. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStars = (interactive = false) => {
    return Array.from({ length: 10 }, (_, index) => {
      const starValue = index + 1;
      const isActive = interactive
        ? hoverRating >= starValue || (!hoverRating && rating >= starValue)
        : rating >= starValue;

      return (
        <button
          key={index}
          type="button"
          onClick={interactive ? () => setRating(starValue) : undefined}
          onMouseEnter={
            interactive ? () => setHoverRating(starValue) : undefined
          }
          onMouseLeave={interactive ? () => setHoverRating(0) : undefined}
          className={`${
            interactive ? "cursor-pointer hover:scale-110" : "cursor-default"
          } transition-all duration-200 ${
            isActive ? "text-yellow-400" : "text-gray-300"
          }`}
          disabled={!interactive}
        >
          <Star className="h-6 w-6 fill-current" />
        </button>
      );
    });
  };

  return (
    <>
      <div className="bg-gradient-to-br from-white to-blue-50/30 rounded-lg shadow-lg border border-blue-200/50 p-6 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
        <div className="text-center">
          {/* Header */}
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-r from-[#2c64fa] to-[#4f7cff] rounded-full flex items-center justify-center shadow-lg">
              <MessageSquare className="h-8 w-8 text-white" />
            </div>
          </div>

          <h3 className="text-xl font-bold bg-gradient-to-r from-[#2c64fa] to-[#4f7cff] bg-clip-text text-transparent mb-3">
            Help Us Improve
          </h3>

          <p className="text-gray-600 mb-6 leading-relaxed">
            Your feedback is invaluable! Help us make the{" "}
            <span className="font-semibold text-[#2c64fa]">Buyers Network</span>{" "}
            better by sharing your experience and suggestions.
          </p>

          {/* Quick Rating Preview */}
          <div className="bg-white/50 rounded-lg p-4 border border-blue-200/30 mb-6">
            <h4 className="font-semibold text-sm text-gray-800 mb-3">
              Rate Your Experience
            </h4>
            <div className="flex justify-center space-x-1 mb-2">
              {renderStars(false)}
            </div>
            <p className="text-xs text-gray-600">
              {rating === 0 ? "Click to rate us!" : `You rated us ${rating}/10`}
            </p>
          </div>

          {/* Features */}

          <button
            onClick={() => setIsModalOpen(true)}
            className="w-full bg-gradient-to-r from-[#2c64fa] to-[#4f7cff] text-white py-3 px-6 rounded-lg hover:from-[#4f7cff] hover:to-[#2c64fa] transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-semibold"
          >
            Share Your Feedback
          </button>
        </div>
      </div>

      {/* Modal */}
      {isModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={() => setIsModalOpen(false)}
        >
          <div
            className="bg-white rounded-lg shadow-xl max-w-[16rem] w-full max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-3 border-b border-gray-200">
              <h2 className="text-lg font-semibold bg-gradient-to-r from-[#2c64fa] to-[#4f7cff] bg-clip-text text-transparent">
                Your Feedback Matters
              </h2>
              <button
                onClick={() => setIsModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Content */}
            <div className="p-3">
              {submitted ? (
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MessageSquare className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Thank You!
                  </h3>
                  <p className="text-gray-600">
                    Your feedback helps us build a better platform for everyone.
                  </p>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Rating */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Rate your experience (1-10)
                    </label>
                    <div className="flex justify-center space-x-1 mb-2">
                      {renderStars(true)}
                    </div>
                    <p className="text-center text-sm text-gray-600">
                      {hoverRating > 0
                        ? `Rating: ${hoverRating}/10`
                        : rating > 0
                        ? `Current rating: ${rating}/10`
                        : "Click on stars to rate"}
                    </p>
                  </div>

                  {/* What don't you like */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      <ThumbsDown className="h-4 w-4 inline mr-1" />
                      What don't you like about our platform?
                    </label>
                    <textarea
                      value={formData.dislikes}
                      onChange={(e) =>
                        setFormData({ ...formData, dislikes: e.target.value })
                      }
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2c64fa]/20 focus:border-[#2c64fa] resize-none text-black"
                      placeholder="Tell us what needs improvement..."
                    />
                  </div>

                  {/* Feature suggestions */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      <Plus className="h-4 w-4 inline mr-1" />
                      What features would you like to see added?
                    </label>
                    <textarea
                      value={formData.suggestions}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          suggestions: e.target.value,
                        })
                      }
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2c64fa]/20 focus:border-[#2c64fa] resize-none text-black"
                      placeholder="Suggest new features or improvements..."
                    />
                  </div>

                  {/* Submit Button */}
                  <button
                    type="submit"
                    disabled={isSubmitting || rating === 0}
                    className="w-full bg-gradient-to-r from-[#2c64fa] to-[#4f7cff] text-white py-2 px-4 rounded-lg hover:from-[#4f7cff] hover:to-[#2c64fa] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed font-semibold flex items-center justify-center space-x-2"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>Submitting...</span>
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4" />
                        <span>Submit Feedback</span>
                      </>
                    )}
                  </button>
                </form>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
