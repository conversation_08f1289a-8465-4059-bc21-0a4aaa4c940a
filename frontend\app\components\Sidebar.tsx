import { Home, Users, Briefcase, TrendingUp, BookOpen, Settings } from 'lucide-react';

export default function Sidebar() {
  const navigationItems = [
    { icon: Home, label: 'Home', active: true },
    { icon: Users, label: 'My Network', count: 12 },
    { icon: Briefcase, label: 'Properties', count: 5 },
    { icon: TrendingUp, label: 'Market Trends' },
    { icon: BookOpen, label: 'Resources' },
    { icon: Settings, label: 'Settings' },
  ];

  return (
    <div>
      {/* Navigation */}
      <div className="bg-gradient-to-br from-white to-green-50/50 rounded-lg shadow-lg border border-green-200/50 backdrop-blur-sm">
        <div className="p-4">
          <nav className="space-y-2">
            {navigationItems.map((item, index) => (
              <a
                key={index}
                href="#"
                className={`flex items-center justify-between px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                  item.active
                    ? 'bg-gradient-to-r from-[#2c6f4a] to-[#3a8b5c] text-white shadow-lg border-r-2 border-[#ffc35a]'
                    : 'text-gray-700 hover:bg-gradient-to-r hover:from-green-50 hover:to-green-100 hover:text-[#2c6f4a] hover:shadow-md'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <item.icon className="h-5 w-5" />
                  <span>{item.label}</span>
                </div>
                {item.count && (
                  <span className="bg-gradient-to-r from-[#ffc35a] to-[#ffb84d] text-white text-xs px-2 py-1 rounded-full shadow-sm">
                    {item.count}
                  </span>
                )}
              </a>
            ))}
          </nav>
        </div>
      </div>
    </div>
  );
}
