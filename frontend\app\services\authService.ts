import { apiService } from "./apiService";

export interface BackendUser {
  id: string;
  email: string;
  phone: string;
  age: number;
  city: string;
  role: "BUYER" | "AGENT" | "DEVELOPER";
}

export interface SignupData {
  email: string;
  password: string;
  confirmPassword: string;
  phone: string;
  age: number;
  city: string;
  role: "BUYER" | "AGENT" | "DEVELOPER";
}

export interface LoginData {
  email: string;
  password: string;
}

export const authService = {
  async signup(data: SignupData) {
    return apiService.post("/api/auth/signup", data);
  },

  async verifySignupOtp(email: string, otp: string) {
    return apiService.post("/api/auth/verify-signup-otp", { email, otp });
  },

  async login(data: LoginData) {
    return apiService.post("/api/auth/login", data);
  },

  async sendOtp(email: string) {
    return apiService.post("/api/auth/send-otp", { email });
  },

  async forgotPassword(email: string) {
    return apiService.post("/api/auth/forgot-password", { email });
  },

  async resetPassword(email: string, otp: string, newPassword: string) {
    return apiService.post("/api/auth/reset-password", {
      email,
      otp,
      newPassword,
    });
  },

  logout() {
    localStorage.removeItem("token");
    localStorage.removeItem("user");
  },

  getStoredUser(): BackendUser | null {
    try {
      const user = localStorage.getItem("user");
      return user ? JSON.parse(user) : null;
    } catch {
      return null;
    }
  },

  storeAuth(token: string, user: BackendUser) {
    localStorage.setItem("token", token);
    localStorage.setItem("user", JSON.stringify(user));
  },
};
