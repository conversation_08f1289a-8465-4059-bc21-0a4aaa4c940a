export const formatTimeAgo = (dateString: string): string => {
  const now = new Date();
  const date = new Date(dateString);
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
  
  return date.toLocaleDateString();
};

export const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'LIKE':
      return '👍';
    case 'COMMENT':
      return '💬';
    case 'REPLY':
      return '↩️';
    case 'COMMENT_THREAD':
      return '🗨️';
    default:
      return '🔔';
  }
};