'use client';

import { useState, useRef, useEffect } from 'react';
import { Globe, ChevronDown } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

export default function LanguageSelector() {
  const { currentLanguage, languages, changeLanguage, t } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleLanguageChange = (language: any) => {
    changeLanguage(language);
    setIsOpen(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative w-full" ref={dropdownRef}>
      <div className="bg-gradient-to-br from-white to-blue-50/50 rounded-lg shadow-lg border border-blue-200/50 backdrop-blur-sm">
        <div className="p-4">
          <div className="flex items-center space-x-2 mb-4">
            <Globe className="h-5 w-5 text-[#2c64fa]" />
            <h3 className="text-sm font-semibold bg-gradient-to-r from-[#2c64fa] to-[#4a7cfc] bg-clip-text text-transparent">
              {t('common.language')}
            </h3>
          </div>

          <div className="relative">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="w-full flex items-center justify-between px-3 py-3 text-sm bg-white border-2 border-gray-200 rounded-lg hover:bg-gray-50 hover:border-[#2c64fa] transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#2c64fa]/20 focus:border-[#2c64fa] shadow-sm hover:shadow-md"
            >
              <div className="flex items-center space-x-3">
                <span className="text-xl">{currentLanguage.flag}</span>
                <span className="text-gray-700 font-medium">{currentLanguage.nativeName}</span>
              </div>
              <ChevronDown className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
            </button>

            {isOpen && (
              <div className="absolute top-full left-0 right-0 mt-2 bg-white border-2 border-gray-200 rounded-lg shadow-xl z-50 max-h-72 overflow-y-auto">
                {languages.map((language) => (
                  <button
                    key={language.code}
                    onClick={() => handleLanguageChange(language)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 text-sm hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100 hover:text-[#2c64fa] transition-all duration-200 border-b border-gray-100 last:border-b-0 ${
                      currentLanguage.code === language.code
                        ? 'bg-gradient-to-r from-[#2c64fa] to-[#4a7cfc] text-white'
                        : 'text-gray-700'
                    }`}
                  >
                    <span className="text-xl">{language.flag}</span>
                    <div className="flex flex-col items-start">
                      <span className="font-medium text-base">{language.nativeName}</span>
                      <span className={`text-xs ${currentLanguage.code === language.code ? 'text-blue-100' : 'text-gray-500'}`}>
                        {language.name}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
