import { apiService } from "./apiService";

export interface Notification {
  id: string;
  userId: string;
  type: "LIKE" | "COMMENT" | "REPLY" | "COMMENT_THREAD";
  message: string;
  questionId: string | null;
  replyId: string | null;
  isRead: boolean;
  createdAt: string;
  actorName?: string;
  actorEmail?: string;
  actorRole?: string;
  questionTitle?: string;
}

export interface NotificationsResponse {
  success: boolean;
  notifications?: Notification[];
  error?: string;
}

export const notificationService = {
  // Get user notifications
  async getNotifications(): Promise<NotificationsResponse> {
    try {
      const response = await apiService.get("/api/notifications");
      let notifications: Notification[] = [];
      if (response.data && typeof response.data === 'object' && 'notifications' in response.data) {
        notifications = (response.data as { notifications: Notification[] }).notifications;
      }
      return {
        success: response.success,
        notifications,
        error: response.error,
      };
    } catch (error) {
      console.error("Error fetching notifications:", error);
      return {
        success: false,
        error: "Failed to fetch notifications",
      };
    }
  },

  // Mark single notification as read
  async markAsRead(notificationId: string) {
    try {
      const response = await apiService.put(
        `/api/notifications/${notificationId}/read`
      );
      return {
        success: response.success,
        error: response.error,
      };
    } catch (error) {
      console.error("Error marking notification as read:", error);
      return {
        success: false,
        error: "Failed to mark as read",
      };
    }
  },

  // Mark all notifications as read
  async markAllAsRead() {
    try {
      const response = await apiService.put("/api/notifications/mark-all-read");
      return {
        success: response.success,
        error: response.error,
      };
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      return {
        success: false,
        error: "Failed to mark all as read",
      };
    }
  },
};
