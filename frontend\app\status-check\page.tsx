"use client";
import { Suspense } from "react";
import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { paymentAPI } from "../utils/api";

function StatusCheckContent() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const txnId = searchParams.get("id");

  useEffect(() => {
    if (!txnId) {
      router.push("/failure");
      return;
    }

    const checkStatus = async () => {
      try {
        await new Promise((resolve) => setTimeout(resolve, 2000));

        const response = await paymentAPI.checkStatus(txnId);
        console.log("Status check response:", response.data);

        // Handle different status responses
        if (
          response.data?.status === "COMPLETED" ||
          response.data?.status === "SUCCESS"
        ) {
          router.push(`/success?id=${txnId}`);
        } else if (
          response.data?.status === "FAILED" ||
          response.data?.status === "FAILURE"
        ) {
          router.push("/failure");
        } else if (response.data?.redirect) {
          const redirectUrl = response.data.redirect;

          // Validate and handle redirect URL
          if (typeof redirectUrl === "string" && redirectUrl.trim()) {
            if (redirectUrl.startsWith("/")) {
              // Relative URL - use router.push
              router.push(redirectUrl);
            } else if (
              redirectUrl.startsWith("http://") ||
              redirectUrl.startsWith("https://")
            ) {
              // Absolute URL - validate before redirecting
              try {
                const validUrl = new URL(redirectUrl);
                window.location.href = validUrl.href;
              } catch (urlError) {
                console.error("Invalid redirect URL:", redirectUrl, urlError);
                router.push("/failure");
              }
            } else {
              console.error("Invalid redirect URL format:", redirectUrl);
              router.push("/failure");
            }
          } else {
            console.error("Empty or invalid redirect URL:", redirectUrl);
            router.push("/failure");
          }
        } else {
          // If status is still pending, redirect to pending page
          router.push(`/pending?id=${txnId}`);
        }
      } catch (error: any) {
        console.error("Status check failed:", error);
        setError(error.message || "Status check failed");
        setTimeout(() => router.push("/failure"), 3000);
      } finally {
        setLoading(false);
      }
    };

    checkStatus();
  }, [txnId, router]);

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center p-8 bg-white rounded-lg shadow-md">
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          <h2 className="text-xl font-bold text-red-600 mb-2">
            Status Check Failed
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <p className="text-sm text-gray-500">
            Redirecting to failure page...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center p-8 bg-white rounded-lg shadow-md">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 className="text-xl font-bold text-gray-800 mb-2">
          Checking Payment Status
        </h2>
        <p className="text-gray-600">
          Please wait while we verify your payment...
        </p>
      </div>
    </div>
  );
}

export default function StatusCheck() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <StatusCheckContent />
    </Suspense>
  );
}
