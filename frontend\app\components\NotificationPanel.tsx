"use client";

import { useState, useEffect, useRef } from "react";
import {
  Bell,
  MessageCircle,
  ArrowUp,
  X,
  Clock,
  User,
  Loader2,
} from "lucide-react";
import { useLanguage } from "../contexts/LanguageContext";
import { useNotifications } from "../hooks/useNotifications";
import { formatTimeAgo, getNotificationIcon } from "../utils/timeUtils";
import { useRouter } from "next/navigation";

interface NotificationPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function NotificationPanel({
  isOpen,
  onClose,
}: NotificationPanelProps) {
  const { t } = useLanguage();
  const router = useRouter();
  const panelRef = useRef<HTMLDivElement>(null);
  const [hasMarkedAsRead, setHasMarkedAsRead] = useState(false);

  const {
    notifications,
    unreadCount,
    loading,
    error,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    markAllAsReadOnView,
  } = useNotifications();

  // Close panel when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        panelRef.current &&
        !panelRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Reset hasMarkedAsRead when panel closes
  useEffect(() => {
    if (!isOpen) {
      setHasMarkedAsRead(false);
    }
  }, [isOpen]);

  useEffect(() => {
    if (isOpen && !hasMarkedAsRead) {
      fetchNotifications().then(() => {
        // Mark all as read when panel is opened and viewed
        const timer = setTimeout(() => {
          markAllAsReadOnView();
          setHasMarkedAsRead(true);
        }, 800);

        return () => clearTimeout(timer);
      });
    }
  }, [isOpen, hasMarkedAsRead, fetchNotifications, markAllAsReadOnView]);

  const handleNotificationClick = async (notification: any) => {
    // Navigate based on notification type
    if (notification.questionId) {
      router.push(`/questions/${notification.questionId}`);

      // If has replyId, scroll to specific reply after navigation
      if (notification.replyId) {
        setTimeout(() => {
          const element = document.getElementById(
            `reply-${notification.replyId}`
          );
          element?.scrollIntoView({ behavior: "smooth" });
        }, 500);
      }
    }

    onClose();
  };

  const getNotificationTypeIcon = (type: string) => {
    switch (type) {
      case "LIKE":
        return <ArrowUp className="h-4 w-4 text-green-600" />;
      case "COMMENT":
        return <MessageCircle className="h-4 w-4 text-blue-600" />;
      case "REPLY":
        return <MessageCircle className="h-4 w-4 text-purple-600" />;
      case "COMMENT_THREAD":
        return <MessageCircle className="h-4 w-4 text-orange-600" />;
      default:
        return <Bell className="h-4 w-4 text-gray-600" />;
    }
  };

  const getNotificationBgColor = (type: string) => {
    switch (type) {
      case "LIKE":
        return "bg-green-100";
      case "COMMENT":
        return "bg-blue-100";
      case "REPLY":
        return "bg-purple-100";
      case "COMMENT_THREAD":
        return "bg-orange-100";
      default:
        return "bg-gray-100";
    }
  };

  if (!isOpen) return null;

  return (
    <div
      ref={panelRef}
      className="z-[999] fixed inset-x-0 bottom-0 top-auto w-full max-w-full min-h-[200px] sm:absolute sm:top-full sm:right-0 sm:left-auto sm:mt-2 sm:w-96 sm:max-w-[24rem] sm:rounded-lg sm:shadow-xl sm:border sm:border-gray-200 max-h-[80vh] sm:max-h-96 rounded-t-xl shadow-2xl border-t border-gray-200 bg-white overflow-y-auto p-0 animate-in fade-in slide-in-from-bottom duration-200"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 sticky top-0 bg-white z-10 rounded-t-xl">
        <div className="flex items-center space-x-2">
          <Bell className="h-5 w-5 text-[#2c6f4a]" />
          <h2 className="text-lg font-semibold">Notifications</h2>
          {unreadCount > 0 && (
            <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
              {unreadCount}
            </span>
          )}
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors text-2xl sm:text-base"
          aria-label="Close notifications"
        >
          <X className="h-5 w-5" />
        </button>
      </div>

      {/* Content */}
      <div className="max-h-80 overflow-y-auto">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center space-x-3">
              <Loader2 className="w-5 h-5 text-[#2c6f4a] animate-spin" />
              <span className="text-gray-600">Loading notifications...</span>
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <div className="text-red-500 mb-2">⚠️</div>
            <h3 className="font-medium text-gray-900 mb-1">Failed to load</h3>
            <p className="text-sm text-gray-500 mb-3">{error}</p>
            <button
              onClick={fetchNotifications}
              className="text-sm text-[#2c6f4a] hover:text-[#3a8b5c] font-medium"
            >
              Try again
            </button>
          </div>
        ) : notifications.length === 0 ? (
          <div className="text-center py-8">
            <Bell className="h-12 w-12 text-gray-300 mx-auto mb-3" />
            <h3 className="font-medium text-gray-900 mb-1">
              No notifications yet
            </h3>
            <p className="text-sm text-gray-500">
              You'll see notifications here when people interact with your posts
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                onClick={() => handleNotificationClick(notification)}
                className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                  !notification.isRead ? "bg-blue-50" : ""
                }`}
              >
                <div className="flex items-start space-x-3">
                  <div
                    className={`p-2 rounded-full ${getNotificationBgColor(
                      notification.type
                    )}`}
                  >
                    {getNotificationTypeIcon(notification.type)}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <div className="flex items-center space-x-1">
                        <User className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-500">
                          {formatTimeAgo(notification.createdAt)}
                        </span>
                      </div>
                      {!notification.isRead && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      )}
                    </div>

                    <div className="text-sm text-gray-800 mb-1">
                      {notification.message}
                    </div>

                    {notification.questionTitle && (
                      <p className="text-xs text-gray-500 truncate">
                        "{notification.questionTitle}"
                      </p>
                    )}

                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                      <Clock className="h-3 w-3" />
                      <span>{formatTimeAgo(notification.createdAt)}</span>
                      <span className="ml-2">
                        {getNotificationIcon(notification.type)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      {notifications.length > 0 && unreadCount > 0 && (
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <button
            onClick={markAllAsRead}
            className="w-full text-sm text-[#2c6f4a] hover:text-[#3a8b5c] font-medium transition-colors"
          >
            Mark all as read
          </button>
        </div>
      )}
    </div>
  );
}
